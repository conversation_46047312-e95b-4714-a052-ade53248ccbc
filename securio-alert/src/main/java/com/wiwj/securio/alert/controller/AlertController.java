package com.wiwj.securio.alert.controller;

import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.core.page.TableDataInfo;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.common.utils.SecurityUtils;
import com.wiwj.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.service.IAlertService;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 告警管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/alert")
public class Alert<PERSON>ontroller extends BaseController {

    @Autowired
    private IAlertService alertService;

    /**
     * 查询告警列表
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:list')")
    @GetMapping("/list")
    public TableDataInfo list(Alert alert) {
        startPage();
        List<Alert> list = alertService.selectAlertList(alert);
        return getDataTable(list);
    }

    /**
     * 导出告警列表
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:export')")
    @Log(title = "告警", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Alert alert) {
        List<Alert> list = alertService.selectAlertList(alert);
        ExcelUtil<Alert> util = new ExcelUtil<Alert>(Alert.class);
        return util.exportExcel(list, "告警数据");
    }

    /**
     * 获取告警详细信息
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(alertService.selectAlertById(id));
    }

    /**
     * 新增告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:add')")
    @Log(title = "告警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Alert alert) {
        alert.setCreateBy(SecurityUtils.getUserId()+"");
        alert.setCreateName(SecurityUtils.getUsername());
        return toAjax(alertService.insertAlert(alert));
    }

    /**
     * 修改告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:edit')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Alert alert) {
        alert.setUpdateBy(SecurityUtils.getUserId()+"");
        alert.setUpdateName(SecurityUtils.getUsername());
        return toAjax(alertService.updateAlert(alert));
    }

    /**
     * 删除告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:remove')")
    @Log(title = "告警", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(alertService.logicDeleteAlertByIds(ids));
    }

    /**
     * 处理告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:resolve')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping("/resolve/{id}")
    public AjaxResult resolve(@PathVariable("id") Long id, @RequestBody Map<String, String> params) {
        String resolutionNote = params.getOrDefault("resolutionNote", "");
        return toAjax(alertService.resolveAlert(id, SecurityUtils.getUserId()+"", SecurityUtils.getUsername(), resolutionNote));
    }

    /**
     * 忽略告警
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:ignore')")
    @Log(title = "告警", businessType = BusinessType.UPDATE)
    @PutMapping("/ignore/{id}")
    public AjaxResult ignore(@PathVariable("id") Long id, @RequestBody Map<String, String> params) {
        String resolutionNote = params.getOrDefault("resolutionNote", "");
        return toAjax(alertService.ignoreAlert(id, SecurityUtils.getUserId()+"", SecurityUtils.getUsername(), resolutionNote));
    }

    /**
     * 接收告警
     *
     * @param alert 告警对象
     * @return 处理结果
     */
    @PostMapping("/push")
    public AjaxResult push(@RequestBody String jsonData) {
        log.info("接收到告警: {}", jsonData);

        return AjaxResult.success();
    }

    /**
     * 接收告警
     *
     * @param alert 告警对象
     * @return 处理结果
     */
    @PostMapping("/receive")
    public AjaxResult receiveAlert(@RequestBody String jsonData) {
        log.info("接收到告警: {}", jsonData);

        return AjaxResult.success();
    }

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果
     */
    @PostMapping("/batch-receive")
    public AjaxResult receiveBatchAlerts(@RequestBody Alert[] alerts) {
        log.info("接收到批量告警, 数量: {}", alerts.length);

        try {
            // 批量接收告警
            Map<String, Object> resultData = alertService.receiveBatchAlerts(alerts);

            // 返回成功结果
            return AjaxResult.success("成功接收批量告警", resultData);
        } catch (Exception e) {
            log.error("批量接收告警时发生错误: {}", e.getMessage(), e);
            return AjaxResult.error("处理告警失败: " + e.getMessage());
        }
    }

    /**
     * 获取告警统计数据
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            Map<String, Object> statistics = alertService.getAlertStatistics();
            return AjaxResult.success("获取统计数据成功", statistics);
        } catch (Exception e) {
            log.error("获取告警统计数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取统计数据失败");
        }
    }

    /**
     * 获取告警趋势数据
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:trend')")
    @GetMapping("/trend")
    public AjaxResult getTrend(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        try {
            List<Map<String, Object>> trendData = alertService.getAlertTrend(days);
            return AjaxResult.success("获取趋势数据成功", trendData);
        } catch (Exception e) {
            log.error("获取告警趋势数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取趋势数据失败");
        }
    }

    /**
     * 获取告警分布数据
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:distribution')")
    @GetMapping("/distribution")
    public AjaxResult getDistribution(@RequestParam(value = "type", defaultValue = "severity") String type) {
        try {
            List<Map<String, Object>> distributionData = alertService.getAlertDistribution(type);
            return AjaxResult.success("获取分布数据成功", distributionData);
        } catch (Exception e) {
            log.error("获取告警分布数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取分布数据失败");
        }
    }

    /**
     * 获取热点告警源
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:top-sources')")
    @GetMapping("/top-sources")
    public AjaxResult getTopSources(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> topSources = alertService.getTopAlertSources(limit);
            return AjaxResult.success("获取热点告警源成功", topSources);
        } catch (Exception e) {
            log.error("获取热点告警源失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取热点告警源失败");
        }
    }

    /**
     * 获取告警处理统计
     */
    @PreAuthorize("@ss.hasPermi('alert:alert:resolution-stats')")
    @GetMapping("/resolution-stats")
    public AjaxResult getResolutionStats(@RequestParam(value = "days", defaultValue = "30") Integer days) {
        try {
            Map<String, Object> resolutionStats = alertService.getResolutionStatistics(days);
            return AjaxResult.success("获取处理统计成功", resolutionStats);
        } catch (Exception e) {
            log.error("获取告警处理统计失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取处理统计失败");
        }
    }
}
