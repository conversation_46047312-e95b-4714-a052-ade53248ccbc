# VictoriaLogs API 说明文档

## 概述

本文档描述了 VictoriaLogs 的 API 接口，这些接口被封装在 `securio-logmgr` 模块中，用于查询和分析日志数据。VictoriaLogs 是一个高性能的日志存储和查询系统，支持使用 LogsQL 查询语言进行复杂的日志查询和分析。

## 基础配置

VictoriaLogs 的配置在 `application-victoria.yml` 文件中：

```yaml
victoria:
  metrics:
    url: http://0.0.0.0:8428
  logs:
    url: http://0.0.0.0:8428
    default-query-timeout: 30
    default-limit: 1000
    default-step: 1h
```

## API 接口

所有 API 接口都位于 `/opslog/victoria` 路径下，支持以下操作：

### 1. 查询日志 `/query`

根据查询条件查询日志数据。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| limit | Integer | 否 | 限制返回的日志条数 |
| timeout | String | 否 | 查询超时时间 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "{\"_msg\":\"error: disconnect from 19.54.37.22: Auth fail [preauth]\",\"_stream\":\"{}\",\"_time\":\"2023-01-01T13:32:13Z\"}\n{\"_msg\":\"some other error\",\"_stream\":\"{}\",\"_time\":\"2023-01-01T13:32:15Z\"}"
}
```

### 2. 实时跟踪日志 `/tail`

实时跟踪日志流，类似于 `tail -f` 命令。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| startOffset | String | 否 | 历史日志时间偏移，例如 "1h" 表示返回过去一小时的日志 |
| offset | String | 否 | 新日志延迟时间 |
| refreshInterval | String | 否 | 刷新间隔 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "实时日志流内容..."
}
```

### 3. 查询日志命中统计 `/hits`

查询日志命中统计数据，按时间分组。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| step | String | 否 | 时间步长，例如 "1h"、"1d" |
| offset | String | 否 | 时区偏移 |
| fields | String[] | 否 | 分组字段列表 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "hits": [
      {
        "fields": {},
        "timestamps": [
          "2024-01-01T00:00:00Z",
          "2024-01-01T01:00:00Z",
          "2024-01-01T02:00:00Z"
        ],
        "values": [
          410339,
          450311,
          899506
        ],
        "total": 1760176
      }
    ]
  }
}
```

### 4. 查询日志字段频率统计 `/facets`

查询日志字段频率统计数据，返回每个字段的最常见值。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| limit | Integer | 否 | 限制返回的字段值数量 |
| maxValuesPerField | Integer | 否 | 每个字段的最大值数量 |
| maxValueLen | Integer | 否 | 字段值的最大长度 |
| keepConstFields | Boolean | 否 | 是否保留常量字段 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "facets": [
      {
        "field_name": "kubernetes_container_name",
        "values": [
          {
            "field_value": "victoria-logs",
            "hits": 442378
          },
          {
            "field_value": "victoria-metrics",
            "hits": 34783
          }
        ]
      }
    ]
  }
}
```

### 5. 查询日志统计 `/stats`

查询日志统计数据，返回与 Prometheus 查询 API 兼容的格式。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法，必须包含 `stats` 管道 |
| time | String | 否 | 查询时间点 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "status": "success",
    "data": {
      "resultType": "vector",
      "result": [
        {
          "metric": {
            "__name__": "count(*)",
            "level": "info"
          },
          "value": [
            1704153600,
            "20395342"
          ]
        }
      ]
    }
  }
}
```

### 6. 查询日志范围统计 `/stats_range`

查询日志范围统计数据，返回与 Prometheus 查询 API 兼容的格式。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法，必须包含 `stats` 管道 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| step | String | 否 | 时间步长，例如 "1h"、"1d" |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "status": "success",
    "data": {
      "resultType": "matrix",
      "result": [
        {
          "metric": {
            "__name__": "count(*)",
            "level": "info"
          },
          "values": [
            [
              1704067200,
              "103125"
            ],
            [
              1704088800,
              "102500"
            ]
          ]
        }
      ]
    }
  }
}
```

### 7. 查询日志流ID `/stream_ids`

查询日志流 ID 列表。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| limit | Integer | 否 | 限制返回的流 ID 数量 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "values": [
      {
        "value": "0000000000000000106955b1744a71b78bd3a88c755751e8",
        "hits": 442953
      },
      {
        "value": "0000000000000000b80988e6012df3520a8e20cd5353c52b",
        "hits": 59349
      }
    ]
  }
}
```

### 8. 查询日志流 `/streams`

查询日志流列表。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| limit | Integer | 否 | 限制返回的流数量 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "values": [
      {
        "value": "{host=\"host-123\",app=\"foo\"}",
        "hits": 34980
      },
      {
        "value": "{host=\"host-124\",app=\"bar\"}",
        "hits": 32892
      }
    ]
  }
}
```

### 9. 查询日志流字段名 `/stream_field_names`

查询日志流字段名列表。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "values": [
      {
        "value": "app",
        "hits": 1033300623
      },
      {
        "value": "container",
        "hits": 1033300623
      }
    ]
  }
}
```

### 10. 查询日志流字段值 `/stream_field_values`

查询日志流字段值列表。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| field | String | 是 | 字段名 |
| limit | Integer | 否 | 限制返回的字段值数量 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "values": [
      {
        "value": "host-1",
        "hits": 69426656
      },
      {
        "value": "host-2",
        "hits": 66507749
      }
    ]
  }
}
```

### 11. 查询日志字段名 `/field_names`

查询日志字段名列表。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "values": [
      {
        "value": "_msg",
        "hits": 1033300623
      },
      {
        "value": "_stream",
        "hits": 1033300623
      },
      {
        "value": "_time",
        "hits": 1033300623
      }
    ]
  }
}
```

### 12. 查询日志字段值 `/field_values`

查询日志字段值列表。

**请求方法**：POST

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| query | String | 是 | 查询语句，使用 LogsQL 语法 |
| start | String | 否 | 开始时间，支持多种时间格式 |
| end | String | 否 | 结束时间，支持多种时间格式 |
| field | String | 是 | 字段名 |
| limit | Integer | 否 | 限制返回的字段值数量 |
| extraFilters | Map | 否 | 额外过滤条件 |
| extraStreamFilters | Map | 否 | 额外流过滤条件 |

**响应示例**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "values": [
      {
        "value": "host-1",
        "hits": 69426656
      },
      {
        "value": "host-2",
        "hits": 66507749
      }
    ]
  }
}
```

## 额外过滤条件

所有 API 接口都支持 `extraFilters` 和 `extraStreamFilters` 参数，用于添加额外的过滤条件。这些参数可以是以下格式：

1. JSON 对象，例如：
   ```json
   {
     "namespace": "my-app",
     "env": "prod"
   }
   ```

2. 数组值，例如：
   ```json
   {
     "namespace": ["my-app", "other-app"],
     "env": "prod"
   }
   ```

3. LogsQL 过滤条件，例如：
   ```
   foo:~bar -baz:x
   ```

## 时间格式

时间参数（`start`、`end`、`time`）支持多种格式：

1. 相对时间：例如 "1h"（1小时前）、"1d"（1天前）、"1w"（1周前）
2. RFC3339 格式：例如 "2024-01-01T12:00:00Z"
3. Unix 时间戳（秒）：例如 "1704110400"

## 查询语法

查询语句使用 LogsQL 语法，详细信息请参考 [VictoriaLogs LogsQL 文档](https://docs.victoriametrics.com/victorialogs/logsql/)。

## 错误处理

当 API 调用失败时，将返回包含错误信息的 JSON 响应：

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": "错误详情"
}
```

## 示例

### 查询包含 "error" 的日志

```
POST /opslog/victoria/query
Content-Type: application/x-www-form-urlencoded

query=error&start=1h&limit=10
```

### 按日志级别统计日志数量

```
POST /opslog/victoria/stats
Content-Type: application/x-www-form-urlencoded

query=* | stats by (level) count(*)
```

### 实时跟踪错误日志

```
POST /opslog/victoria/tail
Content-Type: application/x-www-form-urlencoded

query=error&startOffset=1h&refreshInterval=5s
```
