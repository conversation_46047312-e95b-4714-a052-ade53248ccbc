package com.wiwj.securio.agent.service;

import java.util.List;
import com.wiwj.securio.agent.domain.BaseConfig;

/**
 * agent 基础配置Service接口
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
public interface IBaseConfigService {
    /**
     * 查询agent 基础配置
     *
     * @param id agent 基础配置主键
     * @return agent 基础配置
     */
    public BaseConfig selectBaseConfigById(Long id);

    /**
     * 查询版本号最大的agent 基础配置
     *
     * @return agent 基础配置
     */
    public BaseConfig selectBaseConfigWithMaxVersion();

    /**
     * 查询agent 基础配置列表
     *
     * @param baseConfig agent 基础配置
     * @return agent 基础配置集合
     */
    public List<BaseConfig> selectBaseConfigList(BaseConfig baseConfig);

    /**
     * 新增agent 基础配置
     *
     * @param baseConfig agent 基础配置
     * @return 结果
     */
    public int insertBaseConfig(BaseConfig baseConfig);

    /**
     * 修改agent 基础配置
     *
     * @param baseConfig agent 基础配置
     * @return 结果
     */
    public int updateBaseConfig(BaseConfig baseConfig);

    /**
     * 批量删除agent 基础配置
     *
     * @param ids 需要删除的agent 基础配置主键集合
     * @return 结果
     */
    public int deleteBaseConfigByIds(Long[] ids);

    /**
     * 删除agent 基础配置信息
     *
     * @param id agent 基础配置主键
     * @return 结果
     */
    public int deleteBaseConfigById(Long id);
}
