-- ----------------------------
-- 告警表（新版）
-- ----------------------------
drop table if exists alert;
create table alert (
  id                bigint(20)      not null auto_increment    comment '告警ID',
  title             varchar(255)    not null                   comment '告警标题',
  description       text                                       comment '告警详细描述',
  severity          varchar(20)     not null                   comment '严重程度(critical/high/medium/low/info)',
  status            varchar(20)     not null                   comment '状态(new/processing/resolved/closed/ignored)',
  rule_name         varchar(64)                                comment '告警规则名称',
  group_name        varchar(100)                               comment '告警组名称',
  alert_type        varchar(50)                                comment '告警类型',
  source_ip         varchar(50)                                comment '源IP地址',
  target_ip         varchar(50)                                comment '目标IP地址',
  url               varchar(255)                               comment '告警信息url',
  event_id          varchar(100)                               comment '事件ID，源系统中的唯一标识',
  source_type       varchar(32)     not null                   comment '告警类型 牧云/网宿/n9e等',
  source_ident      varchar(32)     not null                   comment '告警源实例唯一标识',
  source_name       varchar(100)                               comment '告警源实例名称',
  occurred_at       bigint(20)                                 comment '告警发生时间',
  detected_at       bigint(20)                                 comment '告警检测时间',
  resolved_at       bigint(20)                                 comment '告警解决时间',
  resolved_by       varchar(64)                                comment '解决人ID',
  resolved_by_name  varchar(100)                               comment '解决人名称',
  resolution_note   text                                       comment '解决说明',
  duration_ms       bigint(20)                                 comment '告警持续时间（毫秒）',
  tags              json                                       comment '告警标签，JSON格式存储键值对',
  attributes        json                                       comment '告警属性，JSON格式存储复杂对象',
  raw_data          text                                       comment '原始数据',
  create_at         bigint(20)      not null                   comment '创建时间',
  create_by         varchar(64)                                comment '创建人ID',
  create_name       varchar(100)                               comment '创建人名称',
  update_at         bigint(20)                                 comment '更新时间',
  update_by         varchar(64)                                comment '更新人ID',
  update_name       varchar(100)                               comment '更新人名称',
  is_del            tinyint(1)      default 0                  comment '是否删除(0-未删除 1-已删除)',
  primary key (id)
) engine=innodb auto_increment=100 comment = '告警表';

-- 添加索引
create index idx_alert_rule_name on alert (rule_name);
create index idx_alert_group_name on alert (group_name);
create index idx_alert_source_type on alert (source_type);
create index idx_alert_source_ident on alert (source_ident);
create index idx_alert_event_id on alert (event_id);
create index idx_alert_status on alert (status);
create index idx_alert_severity on alert (severity);
create index idx_alert_occurred_at on alert (occurred_at);
create index idx_alert_duration on alert (duration_ms);

-- JSON字段的虚拟列索引（可选，根据实际查询需求添加）
-- ALTER TABLE alert ADD COLUMN environment_tag VARCHAR(50) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment'))) VIRTUAL;
-- CREATE INDEX idx_environment_tag ON alert(environment_tag);
-- ALTER TABLE alert ADD COLUMN service_tag VARCHAR(100) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service'))) VIRTUAL;
-- CREATE INDEX idx_service_tag ON alert(service_tag);
