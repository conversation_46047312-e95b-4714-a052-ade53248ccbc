package com.wiwj.securio.agent.config;

import java.io.Serializable;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 文件完整性监控配置对象
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
public class FimConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 是否启用 */
    @JsonProperty("enabled")
    private Boolean enabled;

    /** 扫描间隔（秒） */
    @JsonProperty("scan_interval")
    private Integer scanInterval;

    /** 是否实时监控 */
    @JsonProperty("realtime")
    private Boolean realtime;

    /** 监控目录列表 */
    @JsonProperty("directories")
    private List<FimDirectory> directories;

    /** 忽略的文件模式列表 */
    @JsonProperty("ignore_files")
    private List<String> ignoreFiles;

    /** 忽略的目录列表 */
    @JsonProperty("ignore_dirs")
    private List<String> ignoreDirs;

    /** 哈希算法 */
    @JsonProperty("hash_algorithm")
    private String hashAlgorithm;

    /** 每秒最大处理文件数 */
    @JsonProperty("max_files_per_second")
    private Integer maxFilesPerSecond;

    /** 每个文件最大事件数 */
    @JsonProperty("max_events_per_file")
    private Integer maxEventsPerFile;

    /** 事件保留时间 */
    @JsonProperty("event_retention")
    private String eventRetention;

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getScanInterval() {
        return scanInterval;
    }

    public void setScanInterval(Integer scanInterval) {
        this.scanInterval = scanInterval;
    }

    public Boolean getRealtime() {
        return realtime;
    }

    public void setRealtime(Boolean realtime) {
        this.realtime = realtime;
    }

    public List<FimDirectory> getDirectories() {
        return directories;
    }

    public void setDirectories(List<FimDirectory> directories) {
        this.directories = directories;
    }

    public List<String> getIgnoreFiles() {
        return ignoreFiles;
    }

    public void setIgnoreFiles(List<String> ignoreFiles) {
        this.ignoreFiles = ignoreFiles;
    }

    public List<String> getIgnoreDirs() {
        return ignoreDirs;
    }

    public void setIgnoreDirs(List<String> ignoreDirs) {
        this.ignoreDirs = ignoreDirs;
    }

    public String getHashAlgorithm() {
        return hashAlgorithm;
    }

    public void setHashAlgorithm(String hashAlgorithm) {
        this.hashAlgorithm = hashAlgorithm;
    }

    public Integer getMaxFilesPerSecond() {
        return maxFilesPerSecond;
    }

    public void setMaxFilesPerSecond(Integer maxFilesPerSecond) {
        this.maxFilesPerSecond = maxFilesPerSecond;
    }

    public Integer getMaxEventsPerFile() {
        return maxEventsPerFile;
    }

    public void setMaxEventsPerFile(Integer maxEventsPerFile) {
        this.maxEventsPerFile = maxEventsPerFile;
    }

    public String getEventRetention() {
        return eventRetention;
    }

    public void setEventRetention(String eventRetention) {
        this.eventRetention = eventRetention;
    }
}