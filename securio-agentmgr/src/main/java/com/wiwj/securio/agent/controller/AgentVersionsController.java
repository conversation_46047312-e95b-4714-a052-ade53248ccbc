package com.wiwj.securio.agent.controller;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.AgentVersions;
import com.wiwj.securio.agent.service.IAgentVersionsService;

import com.wiwj.common.core.page.TableDataInfo;

/**
 * 主机安全 Agent 版本信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@RestController
@RequestMapping("/agent/versions")
public class AgentVersionsController extends BaseController {
    @Autowired
    private IAgentVersionsService agentVersionsService;

    /**
     * 查询主机安全 Agent 版本信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:versions:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgentVersions agentVersions) {
        startPage();
        List<AgentVersions> list = agentVersionsService.selectAgentVersionsList(agentVersions);
        return getDataTable(list);
    }

    /**
     * 获取主机安全 Agent 版本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:versions:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(agentVersionsService.selectAgentVersionsById(id));
    }

    /**
     * 新增主机安全 Agent 版本信息
     */
    @PreAuthorize("@ss.hasPermi('agent:versions:add')")
    @Log(title = "主机安全 Agent 版本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgentVersions agentVersions) {
        return toAjax(agentVersionsService.insertAgentVersions(agentVersions));
    }

    /**
     * 修改主机安全 Agent 版本信息
     */
    @PreAuthorize("@ss.hasPermi('agent:versions:edit')")
    @Log(title = "主机安全 Agent 版本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentVersions agentVersions) {
        return toAjax(agentVersionsService.updateAgentVersions(agentVersions));
    }

    /**
     * 删除主机安全 Agent 版本信息
     */
    @PreAuthorize("@ss.hasPermi('agent:versions:remove')")
    @Log(title = "主机安全 Agent 版本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(agentVersionsService.deleteAgentVersionsByIds(ids));
    }
}
