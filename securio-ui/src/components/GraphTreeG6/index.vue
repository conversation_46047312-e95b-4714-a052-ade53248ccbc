<template>
    <div :id=graphId :ref="graphId" v-loading="loading"></div>
</template>
<script>
import G6, { Tooltip } from '@antv/g6'
import '@/assets/styles/iconfont/iconfont.css'
export default {
  name: "GraphTreeG6",
  props: {
    width:{
      type: Number,
      default: window.innerWidth-550
    },
    height:{
      type: Number,
      default: 500
    },
    renderer:{
      type: String,
      default: 'Canvas'//Canvas  svg卡
    },
    // 数据
    data: {
      type: Object,
      required: true
    },
    // 是否禁用鼠标滚轮缩放
    disableWheelZoom: {
      type: Boolean,
      default: false
    },
    layoutType:{
      type:String,
      default:'compactBox'
    },
    layoutDirection:{
      type:String,
      default:'LR'
    },
    edgeType:{
      type:String,
      default:'cubic-horizontal'
    },
    customTooltipRenderer:{
      type: Function,
      default: function(model){
        const text = model.label;
        return text;
      }
    },
    customEdgeTooltipRenderer:{
      type: Function,
      default: function (model, e) {
        var edge = e.item;
        return edge.getSource().getModel().label + ' <br/> ' + edge.getTarget().getModel().label;
      }

    },
    // 节点配置
    nodeConfig: {
      type: Object,
      default: function() {
        return {
          // 节点大小
          size: [80, 40],
          // 节点样式
          style: {
            fill: '#fff',
            stroke: '#ccc'
          },
        }
    }
  }
  },
  data() {
    return {
      // 遮罩层
      graphId:"mountNode",
      loading: true,
      // width: window.innerWidth-300,
      // height: 500,
      // renderer:'Canvas',//svg卡
      // data:this.data,
      iconExpand:require('@/assets/images/minus.png'), //展开的图片
      iconUnExpand:require('@/assets/images/add.png'),//未展开的图片
      graph: null
    };
  },
  methods: {
    registerGraphNode(){
      // 使用 type: 'iconfont'
      G6.registerNode('iconfont', {
        draw(cfg, group) {
          const {
            backgroundConfig: backgroundStyle,
            style,
            labelCfg: labelStyle,
          } = cfg;

          if (backgroundStyle) {
            group.addShape('circle', {
              attrs: {
                x: 0,
                y: 0,
                r: cfg.size,
                ...backgroundStyle,
              },
            });
          }

          const keyShape = group.addShape('text', {
            attrs: {
              x: 0,
              y: 0,
              fontFamily: 'iconfont', // 对应css里面的font-family: "iconfont";
              textAlign: 'center',
              textBaseline: 'middle',
              text: cfg.text,
              fontSize: cfg.size,
              ...style,
            },
          });
          const labelY = backgroundStyle ? cfg.size * 2 : cfg.size;

          group.addShape('text', {
            attrs: {
              x: 0,
              y: labelY,
              textAlign: 'center',
              text: cfg.label,
              ...labelStyle.style,
            },
          });
          return keyShape;
        },
      });
       // 使用 type: 'rect-with-img'
      G6.registerNode('rect-with-img', {
        draw(cfg, group) {
          const rect = group.addShape('rect', {
            attrs: {
              x: -50,
              y: -50,
              width: 100,
              height: 100,
              fill: '#DEE9FF',
              stroke: '#5B8FF9'
            },
            name: 'rect-shape'
          });

          const img = group.addShape('image', {
            attrs: {
              x: -40,
              y: -40,
              width: 80,
              height: 80,
              img: 'path/to/your/image.png',
              // 还可以添加其他图片样式
            },
            name: 'image-shape'
          });

          return group;
        }
      });
      // 其实可以不用自定义节点，可以使用circle类型的icon字段。但是这种方式，点击节点的时候，里面的icon会存在闪缩的情况
      // https://g6.antv.antgroup.com/manual/middle/elements/nodes/built-in/circle#%E5%9B%BE%E6%A0%87-icon
      G6.registerNode(
        'drag-inner-image-node',
        {
          afterDraw(cfg, group) {
            const size = cfg.size;
            const width = size - 20;
            const height = size - 20;
            const imageShape = group.addShape('image', {
              attrs: {
                x: -width / 2,
                y: -height / 2,
                width,
                height,
                img: cfg.img,
                cursor: 'move',
                labelCfg: {
                  position: 'bottom'
                },
              },
              // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
              name: 'image-shape',
            });
            // 启用拖拽
            imageShape.set('draggable', true);
          },
        },
        'circle',
      );

    },
    registerGraphEdge(){
      const lineDash = [4, 2, 1, 2];
      //line-dash 虚线运动 https://g6.antv.antgroup.com/zh/examples/scatter/edge/#edge
      G6.registerEdge(
        'line-dash',
        {
          afterDraw(cfg, group) {
            // get the first shape in the group, it is the edge's path here=
            const shape = group.get('children')[0];
            let index = 0;
            // Define the animation
            shape.animate(
              () => {
                index++;
                if (index > 9) {
                  index = 0;
                }
                const res = {
                  lineDash,
                  lineDashOffset: -index,
                };
                // returns the modified configurations here, lineDash and lineDashOffset here
                return res;
              },
              {
                repeat: true, // whether executes the animation repeatly
                duration: 3000, // the duration for executing once
              },
            );
          },
        },
        'cubic', // extend the built-in edge 'cubic'
      );
      // arrow-running  箭头沿边运动 https://g6.antv.antgroup.com/zh/examples/scatter/edge/#arrowAnimate
      G6.registerEdge(
        "arrow-running",
        {
          afterDraw(cfg, group) {
            // get the first shape in the group, it is the edge's path here=
            const shape = group.get("children")[0];

            const arrow = group.addShape("marker", {
              attrs: {
                x: 16,
                y: 0,
                r: 8,
                lineWidth: 2,
                stroke: "#3370ff",
                fill: "#fff",
                symbol: (x, y, r) => {
                  return [
                    ["M", x - 6, y - 4],
                    ["L", x - 2, y],
                    ["L", x - 6, y + 4]
                  ];
                }
              }
            });

            // animation for the red circle
            arrow.animate(
              (ratio) => {
                // the operations in each frame. Ratio ranges from 0 to 1 indicating the prograss of the animation. Returns the modified configurations
                // get the position on the edge according to the ratio
                const tmpPoint = shape.getPoint(ratio);
                const pos = getLabelPosition(shape, ratio);
                let matrix = [1, 0, 0, 0, 1, 0, 0, 0, 1];
                matrix = transform(matrix, [
                  ["t", -tmpPoint.x, -tmpPoint.y],
                  ["r", pos.angle],
                  ["t", tmpPoint.x, tmpPoint.y]
                ]);

                // returns the modified configurations here, x and y here
                return {
                  x: tmpPoint.x,
                  y: tmpPoint.y,
                  matrix
                };
              },
              {
                // repeat: true, // Whether executes the animation repeatly
                duration: 3000 // the duration for executing once
              }
            );
          }
        },
        "cubic" // extend the built-in edge 'cubic'
      );
      // circle-running 圆点沿边运动 https://g6.antv.antgroup.com/zh/examples/scatter/edge/#pointInLine
      G6.registerEdge(
        'circle-running',
        {
          afterDraw(cfg, group) {
            // get the first shape in the group, it is the edge's path here=
            const shape = group.get('children')[0];
            // the start position of the edge's path
            const startPoint = shape.getPoint(0);

            // add red circle shape
            const circle = group.addShape('circle', {
              attrs: {
                x: startPoint.x,
                y: startPoint.y,
                fill: '#1890ff',
                r: 3,
              },
              // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
              name: 'circle-shape',
            });

            // animation for the red circle
            circle.animate(
              (ratio) => {
                // the operations in each frame. Ratio ranges from 0 to 1 indicating the prograss of the animation. Returns the modified configurations
                // get the position on the edge according to the ratio
                const tmpPoint = shape.getPoint(ratio);
                // returns the modified configurations here, x and y here
                return {
                  x: tmpPoint.x,
                  y: tmpPoint.y,
                };
              },
              {
                repeat: true, // Whether executes the animation repeatly
                duration: 3000, // the duration for executing once
              },
            );
          },
        },
        'cubic', // extend the built-in edge 'cubic'
      );
    },
    initGraph(){ // 加减https://antv-2018.alipay.com/zh-cn/g6/3.x/demo/tree/custom-tree.html
      this.registerGraphNode();
      this.registerGraphEdge();
      let that =this; // 需要定义,graph实例化后this指向了graph实例
      const tooltip = new G6.Tooltip({
        offsetX: 20,
        offsetY: 30,
        itemTypes: ['node', 'edge'],
        getContent: (e) => {
          const outDiv = document.createElement('div');
          const model = e.item.getModel();
          if (e.item.getType() === 'node') {
            outDiv.innerHTML = this.customTooltipRenderer(model);
          } else {
            outDiv.innerHTML = this.customEdgeTooltipRenderer(model, e);
          }
          return outDiv;
        },
        shouldBegin: (e) => {
          if (e.target.get('name') === 'expand-button-shape') return false;
          return true;
        },
      });
      const graph = new G6.TreeGraph({
          container: this.graphId,
          width: this.width,//宽度
          height: this.height,//高度
          renderer: this.renderer,//。渲染方式选择 SVG 或 Canvas 方式渲染 svg卡
          plugins: [tooltip], // 配置 Tooltip 插件
          modes: {
            default: [
              'drag-canvas',
              'drag-node',
              // 根据 disableWheelZoom 属性决定是否添加 zoom-canvas 模式
              ...(this.disableWheelZoom ? [] : [{
                type: 'zoom-canvas',
                sensitivity: 1.5,
                enableOptimize: true,
              }]),
            ]
          },
          fitView: true,//是否自适应画布
          fitViewPadding:20,//图自适应画布时的四周留白像素值
          maxZoom:1.5, // 最大缩放比例
          minZoom: 0.001, // 最小比例
          layout: {
            type: this.layoutType,//紧凑树
            direction: this.layoutDirection, // 'LR' 根节点在左，往右布局 | 'RL' 根节点在右，往左布局  | 'TB' 根节点在上，往下布局 | 'BT' 根节点在下，往上布局 | 'H' | 'V'
            // nodeSep: 20,
            // rankSep: 100,
            // radial: true,
            defalutPosition: [],
            getId: function getId(d) {
              return d.id;
            },
            getHeight: function getHeight() {
              return 16;
            },
            getWidth: function getWidth() {
              return 16;
            },
            getVGap: function getVGap() {
              return 50;
            },
            getHGap: function getHGap() {
              return 100;
            }
          },
          defaultNode: {
            size: 60,
            labelCfg: {
              style: {
                fill: 'rgba(0, 0, 0, 0.65)',
              }
            },
          },
          // 节点在各状态下的样式
          nodeStateStyles: {
            // hover 状态为 true 时的样式
            hover: {
              fill: 'lightsteelblue',
            },
            // click 状态为 true 时的样式
            click: {
              stroke: '#4586d6',
              lineWidth: 3,
            },
            // selected状态为 true 时的样式
            selected: {
              stroke: '#4586d6',
              fill: 'rgb(255, 255, 255)',
              shadowColor: 'rgba(0,0,0,0.3)',
            }
         },
      });
      that.graph = graph
      // 节点设置
      graph.node(function(node) {
          node.style = {}
          const nodetype = node.type;
          const img = that.$iconOption[node.ci_type]||  that.$iconOption["default"]
          switch(nodetype){
            case 'modelRect':
              node.size = [200, 80];
              node.style= {
                "fill": '#f0f5ff',// 节点填充色
                "stroke": '#409eff',// 节点描边色
                "lineWidth": 2
              }
              node.logoIcon= {// 节点中icon配置
                show: true,// 是否显示icon，值为 false 则不渲染icon
                x: 0,
                y: 0,
                img:img,// 每种类型的icon
                width: 16,
                height: 16,
                offset: 0 // 用于调整图标的左右位置
              }
              node.stateIcon= {// 节点中表示状态的icon配置
                show: true,// 是否显示icon，值为 false 则不渲染icon
                x: 0,
                y: 0,
                img: require('@/assets/images/add.png'),// icon的地址，字符串类型
                width: 16,
                height: 16,
                offset: 8 // 用于调整图标的左右位置
              }
              break;
            case "image":
              node.style ={
                  img: img,
                  fill :'#ecf5ff',// 节点填充色
                  stroke :'#409eff',// 节点描边色
                  width: 30,
                  height: 30
                }
            case "ellipse":
              node.size = [35, 20];
              node.style= {
                  "fill": '#f0f5ff',// 节点填充色
                  "stroke": '#409eff',// 节点描边色
                  "lineWidth": 2
                }
            case "drag-inner-image-node":
              node.img = img;
              node.labelCfg={
                  position: 'bottom'
                }
            }
          return node
      });
        //边设置
      graph.edge(function() {
        return {
          type: that.edgeType,
          color: '#A3B1BF'
        };
      });


      that.loading = false;
      if(this.data){
        graph.data(this.data);
        graph.render();
      }

      //节点点击事件
      graph.on('node:click', evt => {// https://g6.antv.antgroup.com/api/event
        console.log("node:click")
        that.$emit("handleNodeClick",evt);
        // graph.refreshLayout();  //https://www.yuque.com/antv/g6/treegraph
      });

      //节点点击事件
      graph.on('node:dblclick', function(evt) {// https://g6.antv.antgroup.com/api/event
        console.log("node:dblclick")
        var item = evt.item;
        var model = item.getModel();
        var nodeId = model.id;
        const nodeType = model.type;
        // 切换图标
        const collapsed = model.collapsed;
        if (nodeType == "modelRect"){
          if (collapsed == true)
          {
            model["stateIcon"]["img"]= that.iconExpand;
          }
          else{
            model["stateIcon"]["img"]= that.iconUnExpand;
          }
          graph.update(nodeId,model);//更新节点数据 https://www.yuque.com/antv/g6/api-graph
        }
        that.$emit("handleNodeDblClick",evt);
        // graph.refreshLayout();  //https://www.yuque.com/antv/g6/treegraph
        graph.layout()
        // }
      });

    },
    handleUpdateNode(nodeId, nodeData){//更新节点数据
      console.log("更新节点数据")
      this.graph.update(nodeId,nodeData)
    },
    handleAddChild(nodeId, childData){// 给某个节点增加子节点
      console.log("增加子节点")
      this.graph.addChild(childData, nodeId);
    },
    handleGetNodeModule(item){
      return item.getModel();
    },
    handleChangeData(){
      this.graph.changeData(this.data);
      // this.graph.refreshLayout();
      this.graph.layout();
    },
    // 放大
    zoomIn(ratio = 0.1) {
      if (this.graph) {
        const currentZoom = this.graph.getZoom();
        const newZoom = currentZoom * (1 + ratio);
        this.graph.zoomTo(newZoom);
      }
    },
    // 缩小
    zoomOut(ratio = 0.1) {
      if (this.graph) {
        const currentZoom = this.graph.getZoom();
        const newZoom = currentZoom * (1 - ratio);
        this.graph.zoomTo(newZoom);
      }
    },
  },
  mounted() {
    this.initGraph();
  },
  watch:{
    data(newVal, oldVal){
      console.log("数据变更")
      if (this.graph){
        this.handleChangeData();
      }
    }
  }
}
</script>
<style scoped>
  ::v-deep .g6-tooltip {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
</style>
