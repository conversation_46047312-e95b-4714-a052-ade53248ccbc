package com.wiwj.securio.agent.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.InventoryScheduledTaskInfo;
import com.wiwj.securio.agent.service.IInventoryScheduledTaskInfoService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 存储计划任务信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@RestController
@RequestMapping("/agent/scheduledTaskInfo")
public class InventoryScheduledTaskInfoController extends BaseController {
    @Autowired
    private IInventoryScheduledTaskInfoService inventoryScheduledTaskInfoService;

    /**
     * 查询存储计划任务信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:scheduledTaskInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryScheduledTaskInfo inventoryScheduledTaskInfo) {
        startPage();
        List<InventoryScheduledTaskInfo> list = inventoryScheduledTaskInfoService.selectInventoryScheduledTaskInfoList(inventoryScheduledTaskInfo);
        return getDataTable(list);
    }

    /**
     * 导出存储计划任务信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:scheduledTaskInfo:export')")
    @Log(title = "存储计划任务信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryScheduledTaskInfo inventoryScheduledTaskInfo) {
        List<InventoryScheduledTaskInfo> list = inventoryScheduledTaskInfoService.selectInventoryScheduledTaskInfoList(inventoryScheduledTaskInfo);
        ExcelUtil<InventoryScheduledTaskInfo> util = new ExcelUtil<InventoryScheduledTaskInfo>(InventoryScheduledTaskInfo.class);
        util.exportExcel(response, list, "存储计划任务信息数据");
    }

    /**
     * 获取存储计划任务信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:scheduledTaskInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(inventoryScheduledTaskInfoService.selectInventoryScheduledTaskInfoById(id));
    }

    /**
     * 新增存储计划任务信息
     */
    @PreAuthorize("@ss.hasPermi('agent:scheduledTaskInfo:add')")
    @Log(title = "存储计划任务信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryScheduledTaskInfo inventoryScheduledTaskInfo) {
        return toAjax(inventoryScheduledTaskInfoService.insertInventoryScheduledTaskInfo(inventoryScheduledTaskInfo));
    }

    /**
     * 修改存储计划任务信息
     */
    @PreAuthorize("@ss.hasPermi('agent:scheduledTaskInfo:edit')")
    @Log(title = "存储计划任务信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryScheduledTaskInfo inventoryScheduledTaskInfo) {
        return toAjax(inventoryScheduledTaskInfoService.updateInventoryScheduledTaskInfo(inventoryScheduledTaskInfo));
    }

    /**
     * 删除存储计划任务信息
     */
    @PreAuthorize("@ss.hasPermi('agent:scheduledTaskInfo:remove')")
    @Log(title = "存储计划任务信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(inventoryScheduledTaskInfoService.deleteInventoryScheduledTaskInfoByIds(ids));
    }
}
