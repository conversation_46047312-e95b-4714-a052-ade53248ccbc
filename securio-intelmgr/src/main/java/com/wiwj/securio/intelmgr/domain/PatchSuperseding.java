package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 替代补丁对象 patch_superseding
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public class PatchSuperseding extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 补丁ID */
    @Excel(name = "补丁ID")
    private String patchId;

    /** 替代补丁ID */
    @Excel(name = "替代补丁ID")
    private String supersedingId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 英文标题 */
    @Excel(name = "英文标题")
    private String titleEn;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getId(){
        return id;
    }
    public void setPatchId(String patchId){
        this.patchId = patchId;
    }

    public String getPatchId(){
        return patchId;
    }
    public void setSupersedingId(String supersedingId){
        this.supersedingId = supersedingId;
    }

    public String getSupersedingId(){
        return supersedingId;
    }
    public void setTitle(String title){
        this.title = title;
    }

    public String getTitle(){
        return title;
    }
    public void setTitleEn(String titleEn){
        this.titleEn = titleEn;
    }

    public String getTitleEn(){
        return titleEn;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("patchId", getPatchId())
            .append("supersedingId", getSupersedingId())
            .append("title", getTitle())
            .append("titleEn", getTitleEn())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
