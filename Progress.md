{{ ... }}

## 2025-04-29: 实现 DNS 审计功能 - 基础结构搭建

**用户提示**: @[/Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog] 首先阅读 mysql oracle 审计功能的实现，然后开始实现 dns 审计功能，首先创建基础的结构和对应的日志概览组件页，稍后根据我的指示再填充具体的内容

**步骤**:
1.  分析了 `mysql.vue` 和 `oracle.vue` 的实现，确定了通用的 Tab 结构和组件引用模式。
2.  修改了已存在的 [/Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/dns.vue](cci:7://file:///Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/dns.vue:0:0-0:0) 文件，添加了“DNS 日志概览”和“DNS 日志探查”的 Tab 切换功能。
3.  在 `dns.vue` 中引入了 `DnsStatOverview` 组件（用于概览）和通用的 `LogQueryComponent` 组件（用于探查）。
4.  为 `LogQueryComponent` 配置了初步的 DNS 日志字段 (`logSettings`) 和流名称 (`stream="AUDITLOG_DNS_USER"`)。
5.  创建了 DNS 日志概览组件 [/Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/components/dns_stat_overview.vue](cci:7://file:///Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/components/dns_stat_overview.vue:0:0-0:0) 并添加了基础的模板和脚本结构作为占位符。
6.  为 `dns.vue` 添加了与 `mysql.vue` 和 `oracle.vue` 类似的 CSS 样式，确保视觉一致性。

## 2025-04-29: 实现 DNS 审计功能 - 添加错误和命名日志 Tab

**用户提示**: 将 DNS 日志探查 修改成 DNS 查询日志，然后添加两个 tab 分别是 DNS 错误日志，DNS 命名日志 stream 分别是AUDITLOG_DNS_BIND_ERROR、 AUDITLOG_DNS_BIND_NAMED

**步骤**:
1.  修改了 [/Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/dns.vue](cci:7://file:///Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/dns.vue:0:0-0:0) 文件。
2.  将原有的“DNS 日志探查” Tab 重命名为“DNS 查询日志”，对应的 `stream` 保持 `AUDITLOG_DNS_BIND_QUERY` 不变。
3.  新增了“DNS 错误日志” Tab，点击后切换 `activeTab` 为 `error`，并渲染 `log-query-component`，传入 `title="DNS 错误日志"` 和 `stream="AUDITLOG_DNS_BIND_ERROR"`。
4.  新增了“DNS 命名日志” Tab，点击后切换 `activeTab` 为 `named`，并渲染 `log-query-component`，传入 `title="DNS 命名日志"` 和 `stream="AUDITLOG_DNS_BIND_NAMED"`。
5.  暂时为所有三个日志查询 Tab 复用了相同的 `logSettings` 配置，后续可能需要根据不同日志类型的字段进行调整。

## 2025-04-29: 修复 DNS 审计 Tab 切换问题

**用户反馈**: 点击 tab 切换的时候下面的日志查询组件没有刷新

**问题分析**: 当使用 `v-if`/`v-else-if` 切换渲染相同类型的组件 (`log-query-component`) 时，Vue 会复用组件实例，导致实例的 `created`/`mounted` 钩子（通常用于加载数据）不会重新执行。

**解决方案**: 为每个 `log-query-component` 实例添加 `:key="activeTab"` 属性。当 `activeTab` 变化时，`key` 随之变化，强制 Vue 销毁旧实例并创建新实例，从而触发数据重新加载。

**步骤**:
1.  修改了 [/Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/dns.vue](cci:7://file:///Users/<USER>/workspace/securio/securio-manager/securio-ui/src/views/auditlog/dns.vue:0:0-0:0) 文件。
2.  为 `v-else-if="activeTab === 'query'"`, `v-else-if="activeTab === 'error'"` 和 `v-else-if="activeTab === 'named'"` 条件下的 `log-query-component` 分别添加了 `:key="activeTab"` 绑定。