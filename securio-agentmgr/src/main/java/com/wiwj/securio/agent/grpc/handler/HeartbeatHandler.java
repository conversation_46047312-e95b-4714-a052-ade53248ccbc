package com.wiwj.securio.agent.grpc.handler;

import com.wiwj.securio.agent.domain.AgentCommand;
import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.grpc.proto.Command;
import com.wiwj.securio.agent.grpc.proto.HeartbeatRequest;
import com.wiwj.securio.agent.grpc.proto.HeartbeatResponse;
import com.wiwj.securio.agent.service.IAgentCommandService;
import com.wiwj.securio.agent.service.IAgentInfoService;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 心跳请求处理器
 */
@Component
public class HeartbeatHandler implements StreamGrpcRequestHandler<HeartbeatRequest, HeartbeatResponse> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    private static final int MAX_CONNECTIONS_PER_CLIENT = 3; // 每个客户端最大连接数

    // 使用 ConcurrentHashMap 来跟踪客户端连接
    private final ConcurrentHashMap<String, AtomicInteger> clientConnections = new ConcurrentHashMap<>();

    @Autowired
    private IAgentInfoService agentInfoService;

    @Autowired
    private IAgentCommandService agentCommandService;

    /**
     * 验证 token
     *
     * @param token 认证令牌
     * @return 是否有效
     */
    protected boolean validateToken(String token) {
        // TODO: 实现实际的token验证逻辑
        return org.apache.commons.lang3.StringUtils.isNotEmpty(token) && token.startsWith("temp-token-");
    }

    /**
     * 创建心跳请求的 StreamObserver
     *
     * @param responseObserver 响应观察者
     * @return 请求观察者
     */
    public StreamObserver<HeartbeatRequest> createRequestObserver(StreamObserver<HeartbeatResponse> responseObserver) {
        return new StreamObserver<HeartbeatRequest>() {
            private String clientId;
            private boolean isConnected = false;

            @Override
            public void onNext(HeartbeatRequest request) {
                logger.info("Received heartbeat from agent: {}", request.getUuid());
                if (!isConnected) {
                    clientId = request.getUuid();
                    if (!checkAndAddConnection(clientId)) {
                        logger.warn("Too many connections from client: {}", clientId);
                        HeartbeatResponse response = HeartbeatResponse.newBuilder()
                                .setServerTime(getCurrentTime())
                                .setError("Too many connections")
                                .build();
                        responseObserver.onNext(response);
                        responseObserver.onCompleted();
                        return;
                    }
                    isConnected = true;
                    AtomicInteger connections = clientConnections.get(clientId);
                    if (connections != null) {
                        logger.info("New heartbeat connection established for client: {} (total connections: {})",
                                clientId, connections.get());
                    } else {
                        logger.info("New heartbeat connection established for client: {}", clientId);
                    }
                }

                try {
                    // 验证token
                    if (!validateToken(request.getToken())) {
                        HeartbeatResponse response = HeartbeatResponse.newBuilder()
                                .setServerTime(getCurrentTime())
                                .setError("Invalid token")
                                .build();
                        responseObserver.onNext(response);
                        return;
                    }

                    // 更新代理的最后心跳时间
                    try {
                        AgentInfo query = new AgentInfo();
                        query.setAgentId(request.getUuid());
                        List<AgentInfo> existingAgents = agentInfoService.selectAgentInfoList(query);

                        if (existingAgents != null && !existingAgents.isEmpty()) {
                            AgentInfo agentInfo = existingAgents.get(0);
                            agentInfo.setHeartbeatTime(new Date());  // 设置最后心跳时间
                            agentInfo.setUpdateTime(new Date());    // 更新修改时间
                            agentInfo.setUpdateBy("system");
                            agentInfo.setStatus("running");
                            agentInfoService.updateAgentInfo(agentInfo);
                            logger.debug("Updated heartbeat time for agent: {}", request.getUuid());
                        } else {
                            logger.warn("Agent not found in database: {}", request.getUuid());
                        }
                    } catch (Exception e) {
                        logger.error("Error updating heartbeat time for agent: {}", request.getUuid(), e);
                    }

                    // 获取待执行的命令
                    HeartbeatResponse.Builder responseBuilder = HeartbeatResponse.newBuilder()
                            .setServerTime(getCurrentTime())
                            .setError("");

                    try {
                        // 获取该Agent待执行的命令
                        List<AgentCommand> pendingCommands = agentCommandService.getPendingCommands(request.getUuid());
                        List<String> commandIds = new ArrayList<>();

                        if (pendingCommands != null && !pendingCommands.isEmpty()) {
                            logger.info("Found {} pending commands for agent: {}", pendingCommands.size(), request.getUuid());

                            for (AgentCommand agentCommand : pendingCommands) {
                                // 将命令添加到响应中
                                Command.Builder commandBuilder = Command.newBuilder()
                                        .setId(agentCommand.getCommandId())
                                        .setCommand(agentCommand.getCommandName());

                                // 如果有命令参数，解析并添加
                                if (StringUtils.isNotEmpty(agentCommand.getCommandArgs())) {
                                    try {
                                        // 这里假设命令参数是以逗号分隔的字符串，可以根据实际情况调整
                                        String[] args = agentCommand.getCommandArgs().split(",");
                                        for (String arg : args) {
                                            if (StringUtils.isNotEmpty(arg)) {
                                                commandBuilder.addArgs(arg.trim());
                                            }
                                        }
                                    } catch (Exception e) {
                                        logger.warn("Error parsing command args for command {}: {}",
                                                agentCommand.getCommandId(), e.getMessage());
                                    }
                                }

                                responseBuilder.addCommands(commandBuilder.build());
                                commandIds.add(agentCommand.getCommandId());
                            }

                            // 标记命令为已接收状态
                            if (!commandIds.isEmpty()) {
                                agentCommandService.markCommandsAsReceived(commandIds);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("Error retrieving pending commands for agent: {}", request.getUuid(), e);
                    }

                    HeartbeatResponse response = responseBuilder.build();
                    responseObserver.onNext(response);
                } catch (Exception e) {
                    logger.error("Error processing heartbeat for agent: " + request.getUuid(), e);
                    HeartbeatResponse response = HeartbeatResponse.newBuilder()
                            .setServerTime(getCurrentTime())
                            .setError("Internal error: " + e.getMessage())
                            .build();
                    responseObserver.onNext(response);
                }
            }

            @Override
            public void onError(Throwable t) {
                if (isConnected && clientId != null) {
                    removeConnection(clientId);
                    if (t instanceof StatusRuntimeException) {
                        StatusRuntimeException statusException = (StatusRuntimeException) t;
                        if (statusException.getStatus().getCode() == Status.Code.CANCELLED) {
                            logger.info("Client disconnected: {}", clientId);
                        } else {
                            logger.error("Error in heartbeat stream for client: {}. Status: {}, Description: {}",
                                clientId,
                                statusException.getStatus().getCode(),
                                statusException.getStatus().getDescription(),
                                t);
                        }
                    } else {
                        logger.error("Unexpected error in heartbeat stream for client: {}", clientId, t);
                    }
                } else if (isConnected) {
                    logger.error("Unexpected error in heartbeat stream for unknown client", t);
                }
                responseObserver.onError(t);
            }

            @Override
            public void onCompleted() {
                if (isConnected && clientId != null) {
                    removeConnection(clientId);
                    logger.info("Heartbeat stream completed for client: {}", clientId);
                } else if (isConnected) {
                    logger.info("Heartbeat stream completed for unknown client");
                }
                responseObserver.onCompleted();
            }
        };
    }

    private boolean checkAndAddConnection(String clientId) {
        return clientConnections.computeIfAbsent(clientId, k -> new AtomicInteger(0))
                .incrementAndGet() <= MAX_CONNECTIONS_PER_CLIENT;
    }

    private void removeConnection(String clientId) {
        if (clientId != null) {
            clientConnections.computeIfPresent(clientId, (k, v) -> {
                if (v.decrementAndGet() <= 0) {
                    clientConnections.remove(k);
                    return null;
                }
                return v;
            });
        }
    }

    private String getCurrentTime() {
        return LocalDateTime.now().format(DATE_TIME_FORMATTER);
    }
}
