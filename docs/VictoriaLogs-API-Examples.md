# VictoriaLogs API 调用示例

本文档提供了调用 VictoriaLogs API 的示例，这些示例可以直接执行。示例使用 `curl` 命令行工具，您可以在终端中运行这些命令。

## 前提条件

- 确保 VictoriaLogs 服务已启动并可访问
- 确保 securio-logmgr 模块已启动并运行在默认端口 (8233)
- 已安装 curl 和 jq (用于格式化 JSON 输出)

## 基础 URL

所有示例都基于以下基础 URL:

```
http://localhost:8233/opslog/victoria
```

如果您的服务运行在不同的主机或端口上，请相应地调整 URL。

## 1. 查询日志

### 示例 1: 查询包含 "error" 的最近 10 条日志

```bash
curl -X POST "http://localhost:8233/opslog/victoria/query" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "limit=10" | jq
```

### 示例 2: 查询最近 1 小时内包含 "warning" 的日志

```bash
curl -X POST "http://localhost:8233/opslog/victoria/query" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=warning" \
  -d "start=1h" | jq
```

### 示例 3: 查询特定时间范围内的日志

```bash
curl -X POST "http://localhost:8233/opslog/victoria/query" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=2024-01-01T00:00:00Z" \
  -d "end=2024-01-01T01:00:00Z" | jq
```

### 示例 4: 使用额外过滤条件查询日志

```bash
curl -X POST "http://localhost:8233/opslog/victoria/query" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "extraFilters={\"level\":\"error\"}" | jq
```

## 2. 实时跟踪日志

### 示例 1: 实时跟踪包含 "error" 的日志

```bash
curl -X POST "http://localhost:8233/opslog/victoria/tail" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "refreshInterval=5s"
```

### 示例 2: 实时跟踪日志，包括过去 1 小时的历史日志

```bash
curl -X POST "http://localhost:8233/opslog/victoria/tail" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "startOffset=1h" \
  -d "refreshInterval=5s"
```

## 3. 查询日志命中统计

### 示例 1: 查询最近 24 小时内每小时的错误日志数量

```bash
curl -X POST "http://localhost:8233/opslog/victoria/hits" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "start=24h" \
  -d "step=1h" | jq
```

### 示例 2: 按日志级别分组查询命中统计

```bash
curl -X POST "http://localhost:8233/opslog/victoria/hits" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=24h" \
  -d "step=1h" \
  -d "fields=level" | jq
```

## 4. 查询日志字段频率统计

### 示例 1: 查询最近 1 小时内日志字段的频率统计

```bash
curl -X POST "http://localhost:8233/opslog/victoria/facets" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq
```

### 示例 2: 限制每个字段返回的值数量

```bash
curl -X POST "http://localhost:8233/opslog/victoria/facets" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "limit=5" \
  -d "maxValuesPerField=1000" | jq
```

## 5. 查询日志统计

### 示例 1: 按日志级别统计日志数量

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stats" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=* | stats by (level) count(*)" | jq
```

### 示例 2: 查询特定时间点的日志统计

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stats" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=* | stats by (level) count(*)" \
  -d "time=2024-01-01T00:00:00Z" | jq
```

## 6. 查询日志范围统计

### 示例 1: 查询最近 24 小时内每小时的日志数量，按级别分组

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stats_range" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=* | stats by (level) count(*)" \
  -d "start=24h" \
  -d "step=1h" | jq
```

### 示例 2: 查询特定时间范围内的日志统计

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stats_range" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=* | stats by (level) count(*)" \
  -d "start=2024-01-01T00:00:00Z" \
  -d "end=2024-01-02T00:00:00Z" \
  -d "step=6h" | jq
```

## 7. 查询日志流 ID

### 示例 1: 查询最近 1 小时内的日志流 ID

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stream_ids" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq
```

### 示例 2: 限制返回的流 ID 数量

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stream_ids" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "limit=5" | jq
```

## 8. 查询日志流

### 示例 1: 查询最近 1 小时内的日志流

```bash
curl -X POST "http://localhost:8233/opslog/victoria/streams" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq
```

### 示例 2: 查询包含特定错误的日志流

```bash
curl -X POST "http://localhost:8233/opslog/victoria/streams" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "start=1h" \
  -d "limit=10" | jq
```

## 9. 查询日志流字段名

### 示例 1: 查询最近 1 小时内的日志流字段名

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stream_field_names" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq
```

## 10. 查询日志流字段值

### 示例 1: 查询最近 1 小时内 "host" 字段的值

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stream_field_values" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "field=host" | jq
```

### 示例 2: 限制返回的字段值数量

```bash
curl -X POST "http://localhost:8233/opslog/victoria/stream_field_values" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "field=host" \
  -d "limit=5" | jq
```

## 11. 查询日志字段名

### 示例 1: 查询最近 1 小时内的日志字段名

```bash
curl -X POST "http://localhost:8233/opslog/victoria/field_names" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq
```

## 12. 查询日志字段值

### 示例 1: 查询最近 1 小时内 "level" 字段的值

```bash
curl -X POST "http://localhost:8233/opslog/victoria/field_values" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "field=level" | jq
```

### 示例 2: 限制返回的字段值数量

```bash
curl -X POST "http://localhost:8233/opslog/victoria/field_values" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "field=level" \
  -d "limit=5" | jq
```

## 使用 Shell 脚本批量测试

以下是一个简单的 Shell 脚本，可以用来批量测试所有 API 接口：

```bash
#!/bin/bash

BASE_URL="http://localhost:8233/opslog/victoria"

echo "测试查询日志 API..."
curl -s -X POST "$BASE_URL/query" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "limit=5" | jq

echo "测试实时跟踪日志 API (按 Ctrl+C 停止)..."
curl -s -X POST "$BASE_URL/tail" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "refreshInterval=5s" &
TAIL_PID=$!
sleep 5
kill $TAIL_PID

echo "测试查询日志命中统计 API..."
curl -s -X POST "$BASE_URL/hits" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=error" \
  -d "start=1h" \
  -d "step=10m" | jq

echo "测试查询日志字段频率统计 API..."
curl -s -X POST "$BASE_URL/facets" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "limit=3" | jq

echo "测试查询日志统计 API..."
curl -s -X POST "$BASE_URL/stats" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=* | stats by (level) count(*)" | jq

echo "测试查询日志范围统计 API..."
curl -s -X POST "$BASE_URL/stats_range" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=* | stats by (level) count(*)" \
  -d "start=1h" \
  -d "step=10m" | jq

echo "测试查询日志流 ID API..."
curl -s -X POST "$BASE_URL/stream_ids" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "limit=3" | jq

echo "测试查询日志流 API..."
curl -s -X POST "$BASE_URL/streams" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "limit=3" | jq

echo "测试查询日志流字段名 API..."
curl -s -X POST "$BASE_URL/stream_field_names" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq

echo "测试查询日志流字段值 API..."
curl -s -X POST "$BASE_URL/stream_field_values" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "field=host" \
  -d "limit=3" | jq

echo "测试查询日志字段名 API..."
curl -s -X POST "$BASE_URL/field_names" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" | jq

echo "测试查询日志字段值 API..."
curl -s -X POST "$BASE_URL/field_values" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "query=*" \
  -d "start=1h" \
  -d "field=level" \
  -d "limit=3" | jq

echo "测试完成！"
```

将上述脚本保存为 `test_victoria_logs_api.sh`，然后执行以下命令使其可执行并运行：

```bash
chmod +x test_victoria_logs_api.sh
./test_victoria_logs_api.sh
```

## 使用 Python 脚本测试

以下是一个简单的 Python 脚本，可以用来测试 API 接口：

```python
#!/usr/bin/env python3
import requests
import json
import time

BASE_URL = "http://localhost:8233/opslog/victoria"

def print_response(response, title):
    print(f"\n=== {title} ===")
    try:
        print(json.dumps(response.json(), indent=2))
    except:
        print(response.text)
    print("=" * (len(title) + 8))

# 1. 查询日志
response = requests.post(
    f"{BASE_URL}/query",
    data={
        "query": "error",
        "limit": 5
    }
)
print_response(response, "查询日志")

# 2. 查询日志命中统计
response = requests.post(
    f"{BASE_URL}/hits",
    data={
        "query": "error",
        "start": "1h",
        "step": "10m"
    }
)
print_response(response, "查询日志命中统计")

# 3. 查询日志字段频率统计
response = requests.post(
    f"{BASE_URL}/facets",
    data={
        "query": "*",
        "start": "1h",
        "limit": 3
    }
)
print_response(response, "查询日志字段频率统计")

# 4. 查询日志统计
response = requests.post(
    f"{BASE_URL}/stats",
    data={
        "query": "* | stats by (level) count(*)"
    }
)
print_response(response, "查询日志统计")

# 5. 查询日志范围统计
response = requests.post(
    f"{BASE_URL}/stats_range",
    data={
        "query": "* | stats by (level) count(*)",
        "start": "1h",
        "step": "10m"
    }
)
print_response(response, "查询日志范围统计")

# 6. 查询日志流 ID
response = requests.post(
    f"{BASE_URL}/stream_ids",
    data={
        "query": "*",
        "start": "1h",
        "limit": 3
    }
)
print_response(response, "查询日志流 ID")

# 7. 查询日志流
response = requests.post(
    f"{BASE_URL}/streams",
    data={
        "query": "*",
        "start": "1h",
        "limit": 3
    }
)
print_response(response, "查询日志流")

# 8. 查询日志流字段名
response = requests.post(
    f"{BASE_URL}/stream_field_names",
    data={
        "query": "*",
        "start": "1h"
    }
)
print_response(response, "查询日志流字段名")

# 9. 查询日志流字段值
response = requests.post(
    f"{BASE_URL}/stream_field_values",
    data={
        "query": "*",
        "start": "1h",
        "field": "host",
        "limit": 3
    }
)
print_response(response, "查询日志流字段值")

# 10. 查询日志字段名
response = requests.post(
    f"{BASE_URL}/field_names",
    data={
        "query": "*",
        "start": "1h"
    }
)
print_response(response, "查询日志字段名")

# 11. 查询日志字段值
response = requests.post(
    f"{BASE_URL}/field_values",
    data={
        "query": "*",
        "start": "1h",
        "field": "level",
        "limit": 3
    }
)
print_response(response, "查询日志字段值")

print("\n测试完成！")
```

将上述脚本保存为 `test_victoria_logs_api.py`，然后执行以下命令运行：

```bash
python3 test_victoria_logs_api.py
```

## 注意事项

1. 这些示例假设 VictoriaLogs 服务和 securio-logmgr 模块都已正确配置并运行。
2. 如果您的服务运行在不同的主机或端口上，请相应地调整 URL。
3. 某些查询可能需要根据您的实际日志数据进行调整，例如字段名称和查询条件。
4. 对于实时跟踪日志的示例，您可能需要按 Ctrl+C 来停止命令。
5. 使用 `jq` 命令可以格式化 JSON 输出，使其更易读。如果您没有安装 `jq`，可以省略 `| jq` 部分。
