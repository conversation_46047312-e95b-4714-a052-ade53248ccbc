<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.system.mapper.IndexCategoryMapper">
    
    <resultMap type="IndexCategory" id="IndexCategoryResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="ctype"    column="ctype"    />
        <result property="bgStyle"    column="bg_style"    />
        <result property="sortNum"    column="sort_num"    />
        <result property="icon"    column="icon"    />
        <result property="level"    column="level"    />
        <result property="status"    column="status"    />
        <result property="createAt"    column="create_at"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateAt"    column="update_at"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectIndexCategoryVo">
        select id, name, ctype, bg_style, sort_num, icon, level, status, create_at, create_by, update_at, update_by, is_del from index_category
    </sql>

    <select id="selectIndexCategoryList" parameterType="IndexCategory" resultMap="IndexCategoryResult">
        <include refid="selectIndexCategoryVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="ctype != null "> and ctype = #{ctype}</if>
            <if test="bgStyle != null  and bgStyle != ''"> and bg_style = #{bgStyle}</if>
            <if test="sortNum != null "> and sort_num = #{sortNum}</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createAt != null "> and create_at = #{createAt}</if>
            <if test="updateAt != null "> and update_at = #{updateAt}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectIndexCategoryById" parameterType="Long" resultMap="IndexCategoryResult">
        <include refid="selectIndexCategoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertIndexCategory" parameterType="IndexCategory" useGeneratedKeys="true" keyProperty="id">
        insert into index_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="ctype != null">ctype,</if>
            <if test="bgStyle != null">bg_style,</if>
            <if test="sortNum != null">sort_num,</if>
            <if test="icon != null">icon,</if>
            <if test="level != null">level,</if>
            <if test="status != null">status,</if>
            <if test="createAt != null">create_at,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateAt != null">update_at,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="ctype != null">#{ctype},</if>
            <if test="bgStyle != null">#{bgStyle},</if>
            <if test="sortNum != null">#{sortNum},</if>
            <if test="icon != null">#{icon},</if>
            <if test="level != null">#{level},</if>
            <if test="status != null">#{status},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateAt != null">#{updateAt},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateIndexCategory" parameterType="IndexCategory">
        update index_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="ctype != null">ctype = #{ctype},</if>
            <if test="bgStyle != null">bg_style = #{bgStyle},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="level != null">level = #{level},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndexCategoryById" parameterType="Long">
        delete from index_category where id = #{id}
    </delete>

    <delete id="deleteIndexCategoryByIds" parameterType="String">
        delete from index_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>