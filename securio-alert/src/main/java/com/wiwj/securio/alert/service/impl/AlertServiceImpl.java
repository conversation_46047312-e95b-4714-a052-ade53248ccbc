package com.wiwj.securio.alert.service.impl;

import com.alibaba.fastjson2.JSON;
import com.wiwj.common.utils.StringUtils;
import com.wiwj.securio.alert.domain.Alert;
import com.wiwj.securio.alert.domain.AlertOutputConfig;
import com.wiwj.securio.alert.integration.flashduty.service.FlashDutyService;
import com.wiwj.securio.alert.mapper.AlertMapper;
import com.wiwj.securio.alert.service.AlertOutputService;
import com.wiwj.securio.alert.service.IAlertService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AlertServiceImpl implements IAlertService {
    private static final Logger logger = LoggerFactory.getLogger(AlertServiceImpl.class);

    @Autowired
    private AlertMapper alertMapper;

    @Autowired
    private FlashDutyService flashDutyService;

    @Autowired
    private AlertOutputService alertOutputService;


    /**
     * 查询告警
     *
     * @param id 告警ID
     * @return 告警
     */
    @Override
    public Alert selectAlertById(Long id) {
        return alertMapper.selectAlertById(id);
    }

    /**
     * 查询告警列表
     *
     * @param alert 告警
     * @return 告警
     */
    @Override
    public List<Alert> selectAlertList(Alert alert) {
        return alertMapper.selectAlertList(alert);
    }

    /**
     * 新增告警
     *
     * @param alert 告警
     * @return 结果
     */
    @Override
    public int insertAlert(Alert alert) {
        // 设置默认值
        if (alert.getStatus() == null) {
            alert.setStatus("new");
        }
        if (alert.getSeverity() == null) {
            alert.setSeverity("low");
        }
        if (alert.getDetectedAt() == null) {
            alert.setDetectedAt(System.currentTimeMillis());
        }

        // 插入告警记录
        return alertMapper.insertAlert(alert);
    }

    /**
     * 修改告警
     *
     * @param alert 告警
     * @return 结果
     */
    @Override
    public int updateAlert(Alert alert) {
        int rows = alertMapper.updateAlert(alert);

        // 如果告警状态为已解决或已忽略，则发送解决事件到FlashDuty
        if (rows > 0 && (alert.getStatus() != null &&
                ("resolved".equals(alert.getStatus()) || "ignored".equals(alert.getStatus())))) {
            flashDutyService.sendAlertToFlashDuty(alert);
        }

        return rows;
    }

    /**
     * 批量删除告警
     *
     * @param ids 需要删除的告警ID
     * @return 结果
     */
    @Override
    public int deleteAlertByIds(Long[] ids) {
        return alertMapper.deleteAlertByIds(ids);
    }

    /**
     * 删除告警信息
     *
     * @param id 告警ID
     * @return 结果
     */
    @Override
    public int deleteAlertById(Long id) {
        return alertMapper.deleteAlertById(id);
    }

    /**
     * 批量逻辑删除告警
     *
     * @param ids 需要逻辑删除的告警ID
     * @return 结果
     */
    @Override
    public int logicDeleteAlertByIds(Long[] ids) {
        return alertMapper.logicDeleteAlertByIds(ids);
    }

    /**
     * 逻辑删除告警信息
     *
     * @param id 告警ID
     * @return 结果
     */
    @Override
    public int logicDeleteAlertById(Long id) {
        return alertMapper.logicDeleteAlertById(id);
    }

    /**
     * 处理告警
     *
     * @param id             告警ID
     * @param resolvedBy     处理人ID
     * @param resolvedByName 处理人名称
     * @param resolutionNote 处理说明
     * @return 结果
     */
    @Override
    public int resolveAlert(Long id, String resolvedBy, String resolvedByName, String resolutionNote) {
        Alert alert = new Alert();
        alert.setId(id);
        alert.setStatus("resolved");
        alert.setResolvedAt(System.currentTimeMillis());
        alert.setResolvedBy(resolvedBy);
        alert.setResolvedByName(resolvedByName);
        alert.setResolutionNote(resolutionNote);

        return alertMapper.updateAlert(alert);
    }

    /**
     * 忽略告警
     *
     * @param id             告警ID
     * @param resolvedBy     处理人ID
     * @param resolvedByName 处理人名称
     * @param resolutionNote 处理说明
     * @return 结果
     */
    @Override
    public int ignoreAlert(Long id, String resolvedBy, String resolvedByName, String resolutionNote) {
        Alert alert = new Alert();
        alert.setId(id);
        alert.setStatus("ignored");
        alert.setResolvedAt(System.currentTimeMillis());
        alert.setResolvedBy(resolvedBy);
        alert.setResolvedByName(resolvedByName);
        alert.setResolutionNote(resolutionNote);

        return alertMapper.updateAlert(alert);
    }

    /**
     * 处理Webhook推送的告警数据
     * 注意：这个方法已经不再使用，保留是为了兼容旧的API
     *
     * @param token    推送URL令牌
     * @param jsonData JSON数据
     * @return 处理结果数据
     */
    @Override
    public Map<String, Object> processWebhookAlert(String token, String jsonData) throws Exception {
        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("message", "该接口已经不再支持，请使用新的标准告警接口");
        resultData.put("timestamp", System.currentTimeMillis());
        return resultData;
    }

    /**
     * 接收单个告警
     *
     * @param alert 告警对象
     * @return 处理结果数据
     */
    @Override
    public Map<String, Object> receiveAlert(Alert alert) throws Exception {
        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();

        // 设置默认值
        if (alert.getStatus() == null) {
            alert.setStatus("new");
        }
        if (alert.getSeverity() == null) {
            alert.setSeverity("low");
        }
        if (alert.getDetectedAt() == null) {
            alert.setDetectedAt(System.currentTimeMillis());
        }
        if (alert.getCreateAt() == null) {
            alert.setCreateAt(System.currentTimeMillis());
        }
        if (alert.getUpdateAt() == null) {
            alert.setUpdateAt(System.currentTimeMillis());
        }
        if (StringUtils.isEmpty(alert.getTags())) {
            alert.setAttributes("{}");
        }
        if (StringUtils.isEmpty(alert.getAttributes())) {
            alert.setAttributes("{}");
        }
        alert.setIsDel(0);

        // 保存告警
        int result = insertAlert(alert);
        if (result > 0) {
            // 处理告警输出
            processAlertOutput(alert);

            // 添加到结果数据
            resultData.put("alert_id", alert.getId());
        } else {
            throw new Exception("保存告警失败");
        }

        // 返回成功结果
        resultData.put("timestamp", System.currentTimeMillis());

        return resultData;
    }

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果数据
     */
    @Override
    public Map<String, Object> receiveBatchAlerts(Alert[] alerts) throws Exception {
        // 初始化结果数据
        Map<String, Object> resultData = new HashMap<>();
        List<Long> alertIds = new ArrayList<>();

        // 处理每个告警
        for (Alert alert : alerts) {
            try {
                Map<String, Object> result = receiveAlert(alert);
                if (result.containsKey("alert_id")) {
                    alertIds.add((Long) result.get("alert_id"));
                }
            } catch (Exception e) {
                // 记录错误但继续处理
                logger.error("接收告警失败: {}", e.getMessage(), e);
            }
        }

        // 返回成功结果
        resultData.put("count", alertIds.size());
        resultData.put("alert_ids", alertIds);
        resultData.put("timestamp", System.currentTimeMillis());

        return resultData;
    }

    /**
     * 处理告警输出
     * 根据告警的规则名称、源类型和源标识查找匹配的输出配置
     *
     * @param alert 告警对象
     * @return 处理结果
     */
    private boolean processAlertOutput(Alert alert) {
        try {
            // 查询与告警匹配的所有输出配置
            AlertOutputConfig queryConfig = new AlertOutputConfig();
            queryConfig.setAlertRuleName(alert.getRuleName());
            queryConfig.setGroupName(alert.getGroupName());
            queryConfig.setAlertSourceType(alert.getSourceType());
            queryConfig.setAlertSourceIdent(alert.getSourceIdent());
            queryConfig.setStatus("enabled");

            List<AlertOutputConfig> outputConfigs = alertOutputService.selectMatchingOutputConfigs(queryConfig);

            if (outputConfigs == null || outputConfigs.isEmpty()) {
                logger.info("未找到与告警匹配的输出配置，告警ID: {}, 规则名称: {}, 组名称: {}, 源类型: {}, 源标识: {}",
                        alert.getId(), alert.getRuleName(), alert.getGroupName(), alert.getSourceType(), alert.getSourceIdent());
                return false;
            }

            boolean result = true;

            // 遍历所有匹配的输出配置
            for (AlertOutputConfig outputConfig : outputConfigs) {
                // 检查输出配置状态
                if (!"启用".equals(outputConfig.getStatus()) && !"enabled".equals(outputConfig.getStatus())) {
                    logger.info("输出配置已禁用，跳过处理，输出配置ID: {}", outputConfig.getId());
                    continue;
                }

                // 检查是否需要进行标签过滤
                if (outputConfig.getAlertRuleFilter() != null && !outputConfig.getAlertRuleFilter().isEmpty()) {
                    // 基于标签过滤
                    if (!matchTagFilter(alert, outputConfig.getAlertRuleFilter())) {
                        logger.info("告警不匹配标签过滤规则，跳过处理，告警ID: {}, 输出配置ID: {}", alert.getId(), outputConfig.getId());
                        continue;
                    }
                }

                // 根据输出方式处理，目前输出渠道只有flashDuty，不做outputTarget的判断
                if ("forward_raw".equals(outputConfig.getOutputType())) {
                    // 转发元数据
                    logger.info("转发告警原始数据，告警ID: {}, 输出配置ID: {}",
                            alert.getId(), outputConfig.getId());
                    result &= forwardRawData(alert, outputConfig);
                } else if ("send_standard_alert".equals(outputConfig.getOutputType())) {
                    // 发送标准事件
                    logger.info("发送标准告警事件，告警ID: {}, 输出配置ID: {}",
                            alert.getId(), outputConfig.getId());
                    result &= sendStandardAlert(alert, outputConfig);
                }
            }

            return result;
        } catch (Exception e) {
            logger.error("处理告警输出时发生错误", e);
            return false;
        }
    }

    /**
     * 检查告警是否匹配标签过滤规则
     *
     * @param alert          告警对象
     * @param tagFilterRules 标签过滤规则
     * @return 是否匹配
     */
    private boolean matchTagFilter(Alert alert, String tagFilterRules) {
        if (tagFilterRules == null || tagFilterRules.isEmpty()) {
            return true;
        }

        try {
            // 解析标签过滤规则
            Map<String, Object> rules = JSON.parseObject(tagFilterRules);

            // 解析告警标签
            Map<String, String> alertTags = new HashMap<>();
            if (alert.getTags() != null && !alert.getTags().isEmpty()) {
                alertTags = JSON.parseObject(alert.getTags(), Map.class);
            }

            // 检查每个过滤规则
            for (Map.Entry<String, Object> entry : rules.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue().toString();

                // 如果是基本字段，直接检查
                if ("severity".equals(key)) {
                    if (!value.equals(alert.getSeverity())) {
                        return false;
                    }
                } else if ("status".equals(key)) {
                    if (!value.equals(alert.getStatus())) {
                        return false;
                    }
                } else if ("alertType".equals(key)) {
                    if (!value.equals(alert.getAlertType())) {
                        return false;
                    }
                } else if ("sourceType".equals(key)) {
                    if (!value.equals(alert.getSourceType())) {
                        return false;
                    }
                } else if ("sourceIdent".equals(key)) {
                    if (!value.equals(alert.getSourceIdent())) {
                        return false;
                    }
                } else if ("groupName".equals(key)) {
                    if (!value.equals(alert.getGroupName())) {
                        return false;
                    }
                } else {
                    // 否则检查标签
                    if (!alertTags.containsKey(key) || !value.equals(alertTags.get(key))) {
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("解析标签过滤规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送标准告警事件
     *
     * @param alert        告警对象
     * @param outputConfig 输出配置
     * @return 是否成功
     */
    private boolean sendStandardAlert(Alert alert, AlertOutputConfig outputConfig) {
        // 目前只支持 FlashDuty
        String webhookUrl = outputConfig.getOutputWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            logger.error("未配置 Webhook URL，输出配置ID: {}", outputConfig.getId());
            return false;
        }

        return flashDutyService.sendFlashDutyStandardAlert(alert, webhookUrl);
    }

    /**
     * 转发原始数据
     *
     * @param alert        告警对象
     * @param outputConfig 输出配置
     * @return 是否成功
     */
    private boolean forwardRawData(Alert alert, AlertOutputConfig outputConfig) {
        // 获取原始数据
        String rawData = alert.getRawData();
        if (rawData == null || rawData.isEmpty()) {
            logger.warn("告警原始数据为空，无法转发，告警ID: {}", alert.getId());
            return false;
        }

        // 获取 Webhook URL
        String webhookUrl = outputConfig.getOutputWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            logger.error("未配置 Webhook URL，输出配置ID: {}", outputConfig.getId());
            return false;
        }

        // 调用 FlashDutyService 的方法转发原始数据
        return flashDutyService.forwardRawDataToFlashDuty(rawData, webhookUrl, alert.getId());
    }

    /**
     * 获取告警统计数据
     *
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getAlertStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 总告警数
            int totalCount = alertMapper.selectTotalCount();
            statistics.put("total", totalCount);
            
            // 严重告警数（critical）
            int criticalCount = alertMapper.selectCountBySeverity("critical");
            statistics.put("critical", criticalCount);
            
            // 待处理告警数（新建+处理中）
            int pendingCount = alertMapper.selectCountByStatusIn(new String[]{"new", "processing"});
            statistics.put("pending", pendingCount);
            
            // 已解决告警数
            int resolvedCount = alertMapper.selectCountByStatus("resolved");
            statistics.put("resolved", resolvedCount);
            
            // 今日新增告警数
            int todayCount = alertMapper.selectTodayCount();
            statistics.put("today", todayCount);
            
            // 平均处理时间（小时）
            Double avgResolutionTime = alertMapper.selectAvgResolutionTime();
            statistics.put("avgResolutionTime", avgResolutionTime != null ? avgResolutionTime : 0.0);
            
        } catch (Exception e) {
            logger.error("获取告警统计数据时发生错误", e);
            // 返回默认值
            statistics.put("total", 0);
            statistics.put("critical", 0);
            statistics.put("pending", 0);
            statistics.put("resolved", 0);
            statistics.put("today", 0);
            statistics.put("avgResolutionTime", 0.0);
        }
        
        return statistics;
    }

    /**
     * 获取告警趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getAlertTrend(Integer days) {
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        try {
            trendData = alertMapper.selectAlertTrendByDays(days);
        } catch (Exception e) {
            logger.error("获取告警趋势数据时发生错误", e);
        }
        
        return trendData;
    }

    /**
     * 获取告警分布数据
     *
     * @param type 分布类型（severity, status, sourceType等）
     * @return 分布数据
     */
    @Override
    public List<Map<String, Object>> getAlertDistribution(String type) {
        List<Map<String, Object>> distributionData = new ArrayList<>();
        
        try {
            switch (type.toLowerCase()) {
                case "severity":
                    distributionData = alertMapper.selectAlertDistributionBySeverity();
                    break;
                case "status":
                    distributionData = alertMapper.selectAlertDistributionByStatus();
                    break;
                case "sourcetype":
                    distributionData = alertMapper.selectAlertDistributionBySourceType();
                    break;
                default:
                    logger.warn("不支持的分布类型: {}", type);
                    break;
            }
        } catch (Exception e) {
            logger.error("获取告警分布数据时发生错误", e);
        }
        
        return distributionData;
    }

    /**
     * 获取热点告警源
     *
     * @param limit 返回数量限制
     * @return 热点告警源列表
     */
    @Override
    public List<Map<String, Object>> getTopAlertSources(Integer limit) {
        List<Map<String, Object>> topSources = new ArrayList<>();
        
        try {
            topSources = alertMapper.selectTopAlertSources(limit);
        } catch (Exception e) {
            logger.error("获取热点告警源时发生错误", e);
        }
        
        return topSources;
    }

    /**
     * 获取告警处理统计
     *
     * @param days 统计天数
     * @return 处理统计数据
     */
    @Override
    public Map<String, Object> getResolutionStatistics(Integer days) {
        Map<String, Object> resolutionStats = new HashMap<>();
        
        try {
            // 总处理数
            int totalResolved = alertMapper.selectResolvedCountByDays(days);
            resolutionStats.put("totalResolved", totalResolved);
            
            // 平均处理时间
            Double avgTime = alertMapper.selectAvgResolutionTimeByDays(days);
            resolutionStats.put("avgResolutionTime", avgTime != null ? avgTime : 0.0);
            
            // 按处理人分组统计
            List<Map<String, Object>> byResolverStats = alertMapper.selectResolutionStatsByResolver(days);
            resolutionStats.put("byResolver", byResolverStats);
            
            // 按天分组统计
            List<Map<String, Object>> dailyStats = alertMapper.selectDailyResolutionStats(days);
            resolutionStats.put("daily", dailyStats);
            
        } catch (Exception e) {
            logger.error("获取告警处理统计时发生错误", e);
            resolutionStats.put("totalResolved", 0);
            resolutionStats.put("avgResolutionTime", 0.0);
            resolutionStats.put("byResolver", new ArrayList<>());
            resolutionStats.put("daily", new ArrayList<>());
        }
        
        return resolutionStats;
    }
}
