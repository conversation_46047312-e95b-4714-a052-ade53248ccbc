package com.wiwj.securio.intelmgr.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 漏洞详情对象 vuln_detail
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public class VulnDetail extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 漏洞ID */
    private Integer id;

    /** 漏洞描述 */
    @Excel(name = "漏洞描述")
    private String description;

    /** 解决方案 */
    @Excel(name = "解决方案")
    private String solution;

    /** 评估信息 */
    @Excel(name = "评估信息")
    private String assesInfo;

    /** CVSS访问复杂性 */
    @Excel(name = "CVSS访问复杂性")
    private String cvssAccessComplexity;

    /** CVSS认证 */
    @Excel(name = "CVSS认证")
    private String cvssAuthentication;

    /** CVSS机密性影响 */
    @Excel(name = "CVSS机密性影响")
    private String cvssConfidentialityImpact;

    /** CVSS完整性影响 */
    @Excel(name = "CVSS完整性影响")
    private String cvssIntegrityImpact;

    /** CVSS可用性影响 */
    @Excel(name = "CVSS可用性影响")
    private String cvssAvailabilityImpact;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getId(){
        return id;
    }
    public void setDescription(String description){
        this.description = description;
    }

    public String getDescription(){
        return description;
    }
    public void setSolution(String solution){
        this.solution = solution;
    }

    public String getSolution(){
        return solution;
    }
    public void setAssesInfo(String assesInfo){
        this.assesInfo = assesInfo;
    }

    public String getAssesInfo(){
        return assesInfo;
    }
    public void setCvssAccessComplexity(String cvssAccessComplexity){
        this.cvssAccessComplexity = cvssAccessComplexity;
    }

    public String getCvssAccessComplexity(){
        return cvssAccessComplexity;
    }
    public void setCvssAuthentication(String cvssAuthentication){
        this.cvssAuthentication = cvssAuthentication;
    }

    public String getCvssAuthentication(){
        return cvssAuthentication;
    }
    public void setCvssConfidentialityImpact(String cvssConfidentialityImpact){
        this.cvssConfidentialityImpact = cvssConfidentialityImpact;
    }

    public String getCvssConfidentialityImpact(){
        return cvssConfidentialityImpact;
    }
    public void setCvssIntegrityImpact(String cvssIntegrityImpact){
        this.cvssIntegrityImpact = cvssIntegrityImpact;
    }

    public String getCvssIntegrityImpact(){
        return cvssIntegrityImpact;
    }
    public void setCvssAvailabilityImpact(String cvssAvailabilityImpact){
        this.cvssAvailabilityImpact = cvssAvailabilityImpact;
    }

    public String getCvssAvailabilityImpact(){
        return cvssAvailabilityImpact;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("description", getDescription())
            .append("solution", getSolution())
            .append("assesInfo", getAssesInfo())
            .append("cvssAccessComplexity", getCvssAccessComplexity())
            .append("cvssAuthentication", getCvssAuthentication())
            .append("cvssConfidentialityImpact", getCvssConfidentialityImpact())
            .append("cvssIntegrityImpact", getCvssIntegrityImpact())
            .append("cvssAvailabilityImpact", getCvssAvailabilityImpact())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
