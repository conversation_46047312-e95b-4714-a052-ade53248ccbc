package com.wiwj.securio.agent.config;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 传输配置对象
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
public class TransportConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 是否启用 */
    @JsonProperty("enabled")
    private Boolean enabled;

    /** 传输协议 */
    @JsonProperty("protocol")
    private String protocol;

    /** 服务器主机 */
    @JsonProperty("server_host")
    private String serverHost;

    /** 服务器端口 */
    @JsonProperty("server_port")
    private Integer serverPort;

    /** 重试间隔（秒） */
    @JsonProperty("retry_interval")
    private Integer retryInterval;

    /** 最大重试次数 */
    @JsonProperty("max_retries")
    private Integer maxRetries;

    /** 缓冲区大小 */
    @JsonProperty("buffer_size")
    private Integer bufferSize;

    /** 是否压缩数据 */
    @JsonProperty("compress_data")
    private Boolean compressData;

    /** 压缩级别 */
    @JsonProperty("compression_level")
    private Integer compressionLevel;

    /** 心跳间隔（秒） */
    @JsonProperty("heartbeat_interval")
    private Integer heartbeatInterval;

    /** 命令获取间隔（秒） */
    @JsonProperty("command_interval")
    private Integer commandInterval;

    /** 最大并发命令数 */
    @JsonProperty("max_concurrent_cmds")
    private Integer maxConcurrentCmds;

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getServerHost() {
        return serverHost;
    }

    public void setServerHost(String serverHost) {
        this.serverHost = serverHost;
    }

    public Integer getServerPort() {
        return serverPort;
    }

    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    public Integer getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(Integer retryInterval) {
        this.retryInterval = retryInterval;
    }

    public Integer getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
    }

    public Integer getBufferSize() {
        return bufferSize;
    }

    public void setBufferSize(Integer bufferSize) {
        this.bufferSize = bufferSize;
    }

    public Boolean getCompressData() {
        return compressData;
    }

    public void setCompressData(Boolean compressData) {
        this.compressData = compressData;
    }

    public Integer getCompressionLevel() {
        return compressionLevel;
    }

    public void setCompressionLevel(Integer compressionLevel) {
        this.compressionLevel = compressionLevel;
    }

    public Integer getHeartbeatInterval() {
        return heartbeatInterval;
    }

    public void setHeartbeatInterval(Integer heartbeatInterval) {
        this.heartbeatInterval = heartbeatInterval;
    }

    public Integer getCommandInterval() {
        return commandInterval;
    }

    public void setCommandInterval(Integer commandInterval) {
        this.commandInterval = commandInterval;
    }

    public Integer getMaxConcurrentCmds() {
        return maxConcurrentCmds;
    }

    public void setMaxConcurrentCmds(Integer maxConcurrentCmds) {
        this.maxConcurrentCmds = maxConcurrentCmds;
    }
}