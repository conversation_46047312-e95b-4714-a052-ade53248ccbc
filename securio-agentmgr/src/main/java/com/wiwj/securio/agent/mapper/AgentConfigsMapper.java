package com.wiwj.securio.agent.mapper;

import java.util.List;
import com.wiwj.securio.agent.domain.AgentConfigs;

/**
 * Agent 自定义配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
public interface AgentConfigsMapper {
    /**
     * 查询Agent 自定义配置
     *
     * @param id Agent 自定义配置主键
     * @return Agent 自定义配置
     */
    public AgentConfigs selectAgentConfigsById(Long id);

    /**
     * 查询Agent 自定义配置列表
     *
     * @param agentConfigs Agent 自定义配置
     * @return Agent 自定义配置集合
     */
    public List<AgentConfigs> selectAgentConfigsList(AgentConfigs agentConfigs);

    /**
     * 新增Agent 自定义配置
     *
     * @param agentConfigs Agent 自定义配置
     * @return 结果
     */
    public int insertAgentConfigs(AgentConfigs agentConfigs);

    /**
     * 修改Agent 自定义配置
     *
     * @param agentConfigs Agent 自定义配置
     * @return 结果
     */
    public int updateAgentConfigs(AgentConfigs agentConfigs);

    /**
     * 删除Agent 自定义配置
     *
     * @param id Agent 自定义配置主键
     * @return 结果
     */
    public int deleteAgentConfigsById(Long id);

    /**
     * 批量删除Agent 自定义配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAgentConfigsByIds(Long[] ids);

    /**
     * 获取Agent 自定义配置
     *
     * @param agentId Agent唯一标识
     * @return Agent 自定义配置
     */
    public AgentConfigs getAgentConfigs(String agentId);
}
