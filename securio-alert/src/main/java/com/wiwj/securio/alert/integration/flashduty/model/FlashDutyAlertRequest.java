package com.wiwj.securio.alert.integration.flashduty.model;

import java.util.HashMap;
import java.util.Map;

/**
 * FlashDuty告警事件请求
 * 
 * <AUTHOR>
 */
public class FlashDutyAlertRequest {
    
    /** 告警标题 */
    private String title;
    
    /** 告警内容 */
    private String body;
    
    /** 告警优先级 (critical/high/medium/low/info) */
    private String priority;
    
    /** 事件类型 (alert/resolve) */
    private String eventType;
    
    /** 告警来源 */
    private FlashDutySource source;
    
    /** 告警标识，用于关联告警 */
    private String fingerprint;
    
    /** 告警开始时间（Unix时间戳，秒） */
    private Long startTime;
    
    /** 告警结束时间（Unix时间戳，秒） */
    private Long endTime;
    
    /** 告警标签 */
    private Map<String, String> tags;
    
    /** 告警字段 */
    private Map<String, Object> fields;
    
    /** 告警链接 */
    private Map<String, String> links;
    
    public FlashDutyAlertRequest() {
        this.tags = new HashMap<>();
        this.fields = new HashMap<>();
        this.links = new HashMap<>();
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getBody() {
        return body;
    }
    
    public void setBody(String body) {
        this.body = body;
    }
    
    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    public String getEventType() {
        return eventType;
    }
    
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
    
    public FlashDutySource getSource() {
        return source;
    }
    
    public void setSource(FlashDutySource source) {
        this.source = source;
    }
    
    public String getFingerprint() {
        return fingerprint;
    }
    
    public void setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
    }
    
    public Long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    public Long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    public Map<String, String> getTags() {
        return tags;
    }
    
    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }
    
    public void addTag(String key, String value) {
        this.tags.put(key, value);
    }
    
    public Map<String, Object> getFields() {
        return fields;
    }
    
    public void setFields(Map<String, Object> fields) {
        this.fields = fields;
    }
    
    public void addField(String key, Object value) {
        this.fields.put(key, value);
    }
    
    public Map<String, String> getLinks() {
        return links;
    }
    
    public void setLinks(Map<String, String> links) {
        this.links = links;
    }
    
    public void addLink(String name, String url) {
        this.links.put(name, url);
    }
}
