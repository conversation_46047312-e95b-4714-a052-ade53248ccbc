package com.wiwj.securio.agent.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * Agent 自定义配置对象 agent_configs
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
public class AgentConfigs extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 ID */
    private Long id;

    /** agentId */
    @Excel(name = "agentId")
    private String agentId;

    /** agent 主机 IP */
    @Excel(name = "agent 主机 IP")
    private String hostIp;

    /** agent 名称 */
    @Excel(name = "agent 名称")
    private String agentName;

    /** 配置文件内容 */
    @Excel(name = "配置文件内容")
    private String config;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }
    public void setAgentId(String agentId){
        this.agentId = agentId;
    }

    public String getAgentId(){
        return agentId;
    }
    public void setHostIp(String hostIp){
        this.hostIp = hostIp;
    }

    public String getHostIp(){
        return hostIp;
    }
    public void setAgentName(String agentName){
        this.agentName = agentName;
    }

    public String getAgentName(){
        return agentName;
    }
    public void setConfig(String config){
        this.config = config;
    }

    public String getConfig(){
        return config;
    }
    public void setVersion(Long version){
        this.version = version;
    }

    public Long getVersion(){
        return version;
    }
    public void setUpdatedAt(Date updatedAt){
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt(){
        return updatedAt;
    }
    public void setCreatedAt(Date createdAt){
        this.createdAt = createdAt;
    }

    public Date getCreatedAt(){
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentId", getAgentId())
            .append("hostIp", getHostIp())
            .append("agentName", getAgentName())
            .append("config", getConfig())
            .append("version", getVersion())
            .append("updatedAt", getUpdatedAt())
            .append("createdAt", getCreatedAt())
            .toString();
    }
}
