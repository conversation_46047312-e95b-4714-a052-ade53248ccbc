package com.wiwj.securio.alert.mapper;

import java.util.List;
import java.util.Map;
import com.wiwj.securio.alert.domain.Alert;

/**
 * 告警Mapper接口
 *
 * <AUTHOR>
 */
public interface AlertMapper
{
    /**
     * 查询告警
     *
     * @param id 告警ID
     * @return 告警
     */
    public Alert selectAlertById(Long id);

    /**
     * 查询告警列表
     *
     * @param alert 告警
     * @return 告警集合
     */
    public List<Alert> selectAlertList(Alert alert);

    /**
     * 新增告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int insertAlert(Alert alert);

    /**
     * 修改告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int updateAlert(Alert alert);

    /**
     * 删除告警
     *
     * @param id 告警ID
     * @return 结果
     */
    public int deleteAlertById(Long id);

    /**
     * 批量删除告警
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteAlertByIds(Long[] ids);

    /**
     * 逻辑删除告警
     *
     * @param id 告警ID
     * @return 结果
     */
    public int logicDeleteAlertById(Long id);

    /**
     * 批量逻辑删除告警
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int logicDeleteAlertByIds(Long[] ids);

    /**
     * 获取所有非空的标签数据
     *
     * @return 标签JSON字符串列表
     */
    public List<String> selectAllTags();

    // ==================== 统计相关方法 ====================

    /**
     * 查询告警总数
     *
     * @return 告警总数
     */
    public int selectTotalCount();

    /**
     * 根据严重程度查询告警数量
     *
     * @param severity 严重程度
     * @return 告警数量
     */
    public int selectCountBySeverity(String severity);

    /**
     * 根据状态查询告警数量
     *
     * @param status 状态
     * @return 告警数量
     */
    public int selectCountByStatus(String status);

    /**
     * 根据状态列表查询告警数量
     *
     * @param statusList 状态列表
     * @return 告警数量
     */
    public int selectCountByStatusIn(String[] statusList);

    /**
     * 查询今日新增告警数量
     *
     * @return 今日新增告警数量
     */
    public int selectTodayCount();

    /**
     * 查询平均处理时间（小时）
     *
     * @return 平均处理时间
     */
    public Double selectAvgResolutionTime();

    /**
     * 查询告警趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> selectAlertTrendByDays(Integer days);

    /**
     * 根据严重程度查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionBySeverity();

    /**
     * 根据状态查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionByStatus();

    /**
     * 根据告警源类型查询告警分布
     *
     * @return 分布数据
     */
    public List<Map<String, Object>> selectAlertDistributionBySourceType();

    /**
     * 查询热点告警源
     *
     * @param limit 返回数量限制
     * @return 热点告警源列表
     */
    public List<Map<String, Object>> selectTopAlertSources(Integer limit);

    /**
     * 根据天数查询已解决告警数量
     *
     * @param days 天数
     * @return 已解决告警数量
     */
    public int selectResolvedCountByDays(Integer days);

    /**
     * 根据天数查询平均处理时间
     *
     * @param days 天数
     * @return 平均处理时间
     */
    public Double selectAvgResolutionTimeByDays(Integer days);

    /**
     * 根据天数和处理人查询解决统计
     *
     * @param days 天数
     * @return 处理人统计数据
     */
    public List<Map<String, Object>> selectResolutionStatsByResolver(Integer days);

    /**
     * 根据天数查询每日处理统计
     *
     * @param days 天数
     * @return 每日处理统计数据
     */
    public List<Map<String, Object>> selectDailyResolutionStats(Integer days);
}
