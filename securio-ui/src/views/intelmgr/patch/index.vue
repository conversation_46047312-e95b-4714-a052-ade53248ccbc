<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-card class="filter-container" shadow="hover">
      <div class="filter-item-row">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item label="补丁名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入补丁名称" clearable size="small" style="width: 200px" @keyup.enter.native="handleQuery" />
          </el-form-item>
          
          <el-form-item label="补丁编号" prop="number">
            <el-input v-model="queryParams.number" placeholder="请输入补丁编号" clearable size="small" style="width: 150px" @keyup.enter.native="handleQuery" />
          </el-form-item>
          
          <el-form-item label="补丁类型" prop="type">
            <el-select v-model="queryParams.type" placeholder="全部" clearable size="small" style="width: 120px">
              <el-option label="KB" value="KB" />
              <el-option label="安全" value="Security" />
              <el-option label="更新" value="Update" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="风险等级" prop="level">
            <el-select v-model="queryParams.level" placeholder="全部" clearable size="small" style="width: 120px">
              <el-option label="低危" value="1" />
              <el-option label="中危" value="2" />
              <el-option label="高危" value="3" />
              <el-option label="严重" value="4" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="特征标签" prop="feature">
            <el-select v-model="queryParams.feature" placeholder="全部" clearable size="small" style="width: 120px">
              <el-option label="需要重启" value="reboot" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 表格工具栏 -->
    <el-row :gutter="10" class="mb8 action-bar">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['intelmgr:patch:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="small" :disabled="single" @click="handleUpdate" v-hasPermi="['intelmgr:patch:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="small" :disabled="multiple" @click="handleDelete" v-hasPermi="['intelmgr:patch:remove']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格区域 -->
    <el-card shadow="hover" class="table-container">
      <el-table v-loading="loading" :data="patchList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="风险等级" align="center" width="80">
          <template slot-scope="scope">
            <div class="level-badge-wrapper">
              <el-tag :type="getLevelTag(scope.row.level)" effect="dark" size="mini" class="level-badge">{{ getLevelLabel(scope.row.level) }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="补丁类型/编号" align="center" width="160">
          <template slot-scope="scope">
            <div class="patch-info">
              <el-tag size="mini" effect="plain" class="patch-type">{{ scope.row.type || '未知' }}</el-tag>
              <span class="patch-number">{{ scope.row.number }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="补丁名称" align="left" prop="name" min-width="200" show-overflow-tooltip />
        <el-table-column label="特征" align="center" width="80">
          <template slot-scope="scope">
            <div class="feature-tags">
              <el-tag size="mini" type="warning" effect="plain">
                {{ scope.row.feature }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作系统" align="center" prop="os" width="100" />
        <el-table-column label="发布时间" align="center" prop="publishTime" width="120">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['intelmgr:patch:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['intelmgr:patch:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改补丁基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="补丁级别" prop="level">
              <el-select v-model="form.level" placeholder="请选择补丁级别" style="width: 100%">
                <el-option label="低危" value="1" />
                <el-option label="中危" value="2" />
                <el-option label="高危" value="3" />
                <el-option label="严重" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补丁类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择补丁类型" style="width: 100%">
                <el-option label="KB" value="KB" />
                <el-option label="安全" value="Security" />
                <el-option label="更新" value="Update" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="补丁编号" prop="number">
          <el-input v-model="form.number" placeholder="请输入补丁编号" />
        </el-form-item>
        
        <el-form-item label="补丁名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入补丁名称" />
        </el-form-item>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作系统" prop="os">
              <el-input v-model="form.os" placeholder="请输入操作系统" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker clearable
                v-model="form.publishTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择发布时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="补丁描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入补丁描述" />
        </el-form-item>
        
        <el-form-item label="修复建议" prop="solution">
          <el-input v-model="form.solution" type="textarea" :rows="3" placeholder="请输入修复建议" />
        </el-form-item>
        
        <el-form-item label="特征标签" prop="feature">
          <el-checkbox v-model="form.needReboot">
            <el-tag size="mini" type="warning" effect="plain">
              <i class="el-icon-refresh-right"></i> 需要重启
            </el-tag>
          </el-checkbox>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 补丁详情抽屉 -->
    <el-drawer
      :title="detailTitle"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="70%"
      :before-close="handleDrawerClose"
      :destroy-on-close="true"
    >
      <div class="drawer-container" v-loading="drawerLoading">
        <!-- 基本信息部分 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="detail-card-header">
            <span class="detail-card-title">补丁基本信息</span>
            <el-tag v-if="patchDetailForm.feature === 'reboot'" size="small" type="warning" effect="plain" class="reboot-tag">
              <i class="el-icon-refresh-right"></i> 需要重启
            </el-tag>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">补丁名称：</span>
                <span class="detail-content">{{ patchDetailForm.name || '暂无' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">风险等级：</span>
                <el-tag :type="getLevelTag(patchDetailForm.level)" effect="dark" size="mini">{{ getLevelLabel(patchDetailForm.level) }}</el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">补丁类型：</span>
                <el-tag size="mini" effect="plain">{{ patchDetailForm.type || '暂无' }}</el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">补丁编号：</span>
                <span class="detail-content patch-id">{{ patchDetailForm.number || '暂无' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">操作系统：</span>
                <span class="detail-content">{{ patchDetailForm.os || '暂无' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">发布时间：</span>
                <span class="detail-content">{{ parseTime(patchDetailForm.publishTime, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 详细信息 Tabs -->
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 补丁详情选项卡 -->
          <el-tab-pane label="补丁详情" name="patchDetail">
            <el-card class="detail-card" shadow="never">
              <div v-if="isEmpty(patchDetailForm)" class="empty-info">
                <el-empty description="暂无该补丁的详细信息">
                  <template #description>
                    <p>暂无"{{ patchDetailForm.name || '当前补丁' }}"的详细信息</p>
                  </template>
                </el-empty>
              </div>
              
              <template v-else>
                <el-row>
                  <el-col :span="24">
                    <div class="detail-block">
                      <div class="detail-block-title">
                        <i class="el-icon-document"></i> 补丁描述
                      </div>
                      <div v-if="patchDetailForm.description" class="detail-block-content" v-html="patchDetailForm.description"></div>
                      <div v-else class="detail-block-content no-data-content">暂无描述信息</div>
                    </div>
                  </el-col>
                </el-row>
                
                <el-row>
                  <el-col :span="24">
                    <div class="detail-block">
                      <div class="detail-block-title">
                        <i class="el-icon-s-tools"></i> 修复建议
                      </div>
                      <div v-if="patchDetailForm.solution" class="detail-block-content markdown-content" v-html="renderMarkdown(patchDetailForm.solution)"></div>
                      <div v-else class="detail-block-content no-data-content">暂无修复建议</div>
                    </div>
                  </el-col>
                </el-row>
                
                <el-row>
                  <el-col :span="24">
                    <div class="detail-block">
                      <div class="detail-block-title">
                        <i class="el-icon-collection-tag"></i> 特性标签
                      </div>
                      <div class="trait-tags-container">
                        <div v-if="traitList.length > 0" class="trait-tags">
                          <el-tag v-for="item in traitList" :key="item.id" type="warning" effect="plain" size="small" class="trait-tag">
                            <i v-if="item.trait === 'reboot'" class="el-icon-refresh-right"></i>
                            {{ item.trait }}
                          </el-tag>
                        </div>
                        <div v-else class="detail-block-content no-data-content">暂无特性标签</div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
            </el-card>
          </el-tab-pane>
          
          <!-- CVE选项卡 -->
          <el-tab-pane label="CVE信息" name="cveInfo">
            <el-card class="detail-card" shadow="never">
              <div v-if="cveList.length === 0" class="empty-info">
                <el-empty description="暂无该补丁的CVE信息">
                  <template #description>
                    <p>暂无"{{ patchDetailForm.name || '当前补丁' }}"的CVE信息</p>
                  </template>
                </el-empty>
              </div>
              <template v-else>
                <el-table :data="cveList" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="50" align="center" />
                  <el-table-column label="CVE编号" align="left" prop="cve" min-width="180">
                    <template slot-scope="scope">
                      <el-link :href="`https://cve.mitre.org/cgi-bin/cvename.cgi?name=${scope.row.cve}`" type="primary" target="_blank">
                        <i class="el-icon-link"></i> {{ scope.row.cve }}
                      </el-link>
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间" align="center" width="180">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.createdAt) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-card>
          </el-tab-pane>
          
          <!-- 影响产品选项卡 -->
          <el-tab-pane label="影响产品" name="affectProducts">
            <el-card class="detail-card" shadow="never">
              <div v-if="affectProductList.length === 0" class="empty-info">
                <el-empty description="暂无该补丁的影响产品信息">
                  <template #description>
                    <p>暂无"{{ patchDetailForm.name || '当前补丁' }}"的影响产品信息</p>
                  </template>
                </el-empty>
              </div>
              <template v-else>
                <el-table :data="affectProductList" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="50" align="center" />
                  <el-table-column label="产品名称" align="left" prop="product" min-width="250">
                    <template slot-scope="scope">
                      <el-tag size="medium" effect="plain">{{ scope.row.product }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间" align="center" width="180">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.createdAt) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-card>
          </el-tab-pane>
          
          <!-- 影响包选项卡 -->
          <el-tab-pane label="影响包" name="affectPackages">
            <el-card class="detail-card" shadow="never">
              <div v-if="affectPackageList.length === 0" class="empty-info">
                <el-empty description="暂无该补丁的影响包信息">
                  <template #description>
                    <p>暂无"{{ patchDetailForm.name || '当前补丁' }}"的影响包信息</p>
                  </template>
                </el-empty>
              </div>
              <template v-else>
                <el-table :data="affectPackageList" style="width: 100%" border>
                  <el-table-column label="序号" type="index" width="50" align="center" />
                  <el-table-column label="包名称" align="left" prop="packageName" min-width="250">
                    <template slot-scope="scope">
                      <el-tag size="medium" effect="plain" type="info">{{ scope.row.packageName }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间" align="center" width="180">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.createdAt) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-card>
          </el-tab-pane>
          
          <!-- 补丁替代关系选项卡 -->
          <el-tab-pane label="替代关系" name="supersededInfo">
            <el-card class="detail-card" shadow="never">
              <div v-if="supersededList.length === 0 && supersedingList.length === 0" class="empty-info">
                <el-empty description="暂无该补丁的替代关系信息">
                  <template #description>
                    <p>暂无"{{ patchDetailForm.name || '当前补丁' }}"的替代关系信息</p>
                  </template>
                </el-empty>
              </div>
              <template v-else>
                <!-- 被替代的补丁 -->
                <div v-if="supersededList.length > 0" class="detail-block">
                  <div class="detail-block-title">
                    <i class="el-icon-arrow-down"></i> 当前补丁替代了以下补丁
                  </div>
                  <el-table :data="supersededList" style="width: 100%" border size="small">
                    <el-table-column label="序号" type="index" width="50" align="center" />
                    <el-table-column label="被替代补丁ID" align="center" prop="supersededId" width="180" />
                    <el-table-column label="标题" align="left" prop="title" min-width="250" />
                    <el-table-column label="创建时间" align="center" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.createdAt) }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                
                <!-- 替代补丁 -->
                <div v-if="supersedingList.length > 0" class="detail-block">
                  <div class="detail-block-title">
                    <i class="el-icon-arrow-up"></i> 当前补丁被以下补丁替代
                  </div>
                  <el-table :data="supersedingList" style="width: 100%" border size="small">
                    <el-table-column label="序号" type="index" width="50" align="center" />
                    <el-table-column label="替代补丁ID" align="center" prop="supersedingId" width="180" />
                    <el-table-column label="标题" align="left" prop="title" min-width="250" />
                    <el-table-column label="创建时间" align="center" width="180">
                      <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.createdAt) }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-card>
          </el-tab-pane>
            
          <!-- 参考链接选项卡 -->
          <el-tab-pane label="参考链接" name="referenceLinks">
            <el-card class="detail-card" shadow="never">
              <div v-if="referenceList.length === 0" class="empty-info">
                <el-empty description="暂无该补丁的参考链接数据">
                  <template #description>
                    <p>暂无"{{ patchDetailForm.name || '当前补丁' }}"的参考链接数据</p>
                  </template>
                </el-empty>
              </div>
              <el-table v-else :data="referenceList" style="width: 100%" border>
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="来源" align="left" prop="source" min-width="120">
                  <template slot-scope="scope">
                    <span v-if="scope.row.source">{{ scope.row.source }}</span>
                    <span v-else class="no-data">未提供来源</span>
                  </template>
                </el-table-column>
                <el-table-column label="标题" align="left" prop="title" min-width="180">
                  <template slot-scope="scope">
                    <span v-if="scope.row.title">{{ scope.row.title }}</span>
                    <span v-else class="no-data">未提供标题</span>
                  </template>
                </el-table-column>
                <el-table-column label="参考链接" align="left" prop="link" min-width="250">
                  <template slot-scope="scope">
                    <a v-if="scope.row.link" :href="scope.row.link" target="_blank" class="reference-link">
                      <i class="el-icon-link"></i> {{ scope.row.link }}
                    </a>
                    <span v-else class="no-data">未提供链接</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { listPatch, getPatch, delPatch, addPatch, updatePatch, getPatchDetail } from "@/api/intelmgr/patch";
import { marked } from 'marked';

export default {
  name: "Patch",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 补丁基本信息表格数据
      patchList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 抽屉相关数据
      drawerVisible: false,
      drawerLoading: false,
      detailTitle: "补丁详情",
      patchDetailForm: {},
      // Tab页相关
      activeTab: "patchDetail",
      // 参考链接相关
      referenceLoading: false,
      referenceList: [],
      // 补丁关联数据
      traitList: [],        // 特性列表
      affectProductList: [], // 影响产品列表
      affectPackageList: [], // 影响包列表
      cveList: [],          // CVE列表
      supersededList: [],    // 被替代的补丁列表
      supersedingList: [],   // 替代补丁列表
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        level: null,
        type: null,
        number: null,
        name: null,
        os: null,
        publishTime: null,
        feature: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        level: [
          { required: true, message: "补丁级别不能为空", trigger: "change" }
        ],
        type: [
          { required: true, message: "补丁类型不能为空", trigger: "change" }
        ],
        number: [
          { required: true, message: "补丁编号不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "补丁名称不能为空", trigger: "blur" }
        ],
        os: [
          { required: true, message: "操作系统不能为空", trigger: "blur" }
        ],
        publishTime: [
          { required: true, message: "发布时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // u6e32u67d3Markdownu6587u672c
    renderMarkdown(text) {
      if (!text) return '';
      return marked(text);
    },
    // 根据级别获取标签类型
    getLevelTag(level) {
      if (level === 4 || level === '4') return 'danger';
      if (level === 3 || level === '3') return 'warning';
      if (level === 2 || level === '2') return 'info';
      if (level === 1 || level === '1') return 'success';
      return '';
    },
    // 根据级别获取标签文本
    getLevelLabel(level) {
      if (level === 4 || level === '4') return '严重';
      if (level === 3 || level === '3') return '高危';
      if (level === 2 || level === '2') return '中危';
      if (level === 1 || level === '1') return '低危';
      return '未知';
    },
    /** 查询补丁基本信息列表 */
    getList() {
      this.loading = true;
      listPatch(this.queryParams).then(response => {
        this.patchList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 查看详情
    handleViewDetail(row) {
      this.drawerVisible = true;
      this.drawerLoading = true;
      this.activeTab = "patchDetail";
      
      // 重置数据
      this.patchDetailForm = {};
      this.referenceList = [];
      
      // 获取补丁详细信息
      getPatchDetail(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.patchDetailForm = response.data.basic;
          this.detailTitle = `补丁详情: ${this.patchDetailForm.name || row.id}`;
          
          // 设置参考链接数据
          this.referenceList = response.data.references || [];
          
          // 设置其他关联数据
          this.traitList = response.data.traits || [];
          this.affectProductList = response.data.affectProducts || [];
          this.affectPackageList = response.data.affectPackages || [];
          this.cveList = response.data.cves || [];
          this.supersededList = response.data.superseded || [];
          this.supersedingList = response.data.superseding || [];
        } else {
          this.$message.error('获取补丁详细信息失败');
        }
        this.drawerLoading = false;
      }).catch(error => {
        console.error('获取补丁详细信息出错:', error);
        this.$message.error('获取补丁信息失败，请稍后重试');
        this.drawerLoading = false;
      });
    },
    
    // 关闭抽屉
    handleDrawerClose(done) {
      this.patchDetailForm = {};
      this.referenceList = [];
      this.traitList = [];
      this.affectProductList = [];
      this.affectPackageList = [];
      this.cveList = [];
      this.supersededList = [];
      this.supersedingList = [];
      this.activeTab = "patchDetail";
      done();
    },
    
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        level: null,
        type: "KB",
        number: null,
        name: null,
        os: null,
        publishTime: null,
        description: null,
        solution: null,
        needReboot: false,
        feature: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        level: null,
        type: null,
        number: null,
        name: null,
        os: null,
        publishTime: null,
        feature: null
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加补丁基本信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPatch(id).then(response => {
        this.form = response.data;
        this.form.needReboot = this.form.feature === 'reboot';
        this.open = true;
        this.title = "修改补丁基本信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理特征标签
          if (this.form.needReboot) {
            this.form.feature = 'reboot';
          } else {
            this.form.feature = null;
          }
          
          if (this.form.id != null) {
            updatePatch(this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "修改失败");
              }
            }).catch(error => {
              console.error("修改补丁信息出错:", error);
              this.$modal.msgError("修改失败，请稍后重试");
            });
          } else {
            addPatch(this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "新增失败");
              }
            }).catch(error => {
              console.error("新增补丁信息出错:", error);
              this.$modal.msgError("新增失败，请稍后重试");
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除补丁基本信息编号为"' + ids + '"的数据项？').then(function() {
        return delPatch(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 检查数据是否为空
    isEmpty(data) {
      if (!data) return true;
      return Object.keys(data).length === 0;
    }
  }
};
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}
.filter-tag {
  background-color: transparent;
  border: none;
  margin-right: 0;
  padding-right: 0;
}
.action-bar {
  margin-bottom: 20px;
}
.table-container {
  margin-bottom: 20px;
}

/* 风险等级样式 */
.level-badge-wrapper {
  display: flex;
  justify-content: center;
}
.level-badge {
  min-width: 40px;
  text-align: center;
  font-weight: bold;
}

/* 补丁类型和编号样式 */
.patch-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
.patch-type {
  font-size: 10px;
  padding: 0 6px;
}
.patch-number {
  font-weight: bold;
  font-size: 12px;
}

/* 特征标签样式 */
.feature-tags {
  display: flex;
  justify-content: center;
  gap: 4px;
}

/* 详情抽屉样式 */
.drawer-container {
  padding: 20px;
}
.detail-card {
  margin-bottom: 20px;
}
.detail-card-header {
  display: flex;
  align-items: center;
}
.detail-card-title {
  font-size: 16px;
  font-weight: bold;
  margin-right: 10px;
}
.reboot-tag {
  margin-left: auto;
}
.detail-item {
  margin-bottom: 12px;
  line-height: 1.2;
  display: flex;
  align-items: center;
}
.detail-label {
  font-weight: bold;
  font-size: 12px;
  margin-right: 8px;
  color: #606266;
  min-width: 70px;
}
.detail-content {
  color: #303133;
  font-size: 12px;
}
.patch-id {
  font-weight: bold;
  color: #409EFF;
}
.detail-block {
  margin-bottom: 20px;
}
.detail-block-title {
  font-weight: bold;
  color: #606266;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  gap: 5px;
}
.detail-block-content {
  line-height: 1.8;
  color: #303133;
  white-space: pre-line;
  font-size: 12px;
  padding: 5px;
}

/* 新增的样式 */
.empty-info {
  text-align: center;
  padding: 20px;
}
.no-data {
  color: #909399;
  font-size: 12px;
}
.no-data-content {
  color: #909399;
  font-style: italic;
  font-size: 12px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

/* 参考链接样式 */
.reference-link {
  color: #409eff;
  text-decoration: none;
  font-size: 12px;
}
.reference-link:hover {
  text-decoration: underline;
}

/* 确保表格内容字体为12px */
.el-table {
  font-size: 12px;
}

/* 选项卡内容最小高度，避免空内容时过于压缩 */
.el-tab-pane {
  min-height: 200px;
}

/* 特性标签样式 */
.trait-tags-container {
  margin-top: 10px;
}
.trait-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 5px;
}
.trait-tag {
  margin-right: 5px;
}

/* Markdown样式 */
::v-deep .markdown-content {
  line-height: 1.6;
  word-wrap: break-word;
}

::v-deep .markdown-content h1,
::v-deep .markdown-content h2,
::v-deep .markdown-content h3,
::v-deep .markdown-content h4,
::v-deep .markdown-content h5,
::v-deep .markdown-content h6 {
  margin-top: 0.5em;
  margin-bottom: 0.2em;
  font-weight: 600;
}

::v-deep .markdown-content h1 { font-size: 1.8em; }
::v-deep .markdown-content h2 { font-size: 1.5em; }
::v-deep .markdown-content h3 { font-size: 1.3em; }
::v-deep .markdown-content h4 { font-size: 1.2em; }
::v-deep .markdown-content h5 { font-size: 1.1em; }
::v-deep .markdown-content h6 { font-size: 1em; }

::v-deep .markdown-content p {
  margin: 0em 0;
}

::v-deep .markdown-content ul,
::v-deep .markdown-content ol {
  padding-left: 2em;
  margin: 0.5em 0;
}

::v-deep .markdown-content ul > li {
  list-style-type: disc;
}

::v-deep .markdown-content ol > li {
  list-style-type: decimal;
}

::v-deep .markdown-content li {
  margin: 0.5em 0;
}

::v-deep .markdown-content blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  color: #666;
  background-color: #f8f8f8;
  border-left: 4px solid #ddd;
}

::v-deep .markdown-content code {
  padding: 2px 4px;
  font-family: 'Courier New', monospace;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}

::v-deep .markdown-content pre {
  margin: 1em 0;
  padding: 1em;
  overflow: auto;
  background-color: #f8f8f8;
  border-radius: 4px;
}

::v-deep .markdown-content pre code {
  padding: 0;
  color: inherit;
  background-color: transparent;
  border-radius: 0;
}

::v-deep .markdown-content a {
  color: #409eff;
  text-decoration: none;
}

::v-deep .markdown-content a:hover {
  text-decoration: underline;
}

::v-deep .markdown-content img {
  max-width: 100%;
}

::v-deep .markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

::v-deep .markdown-content table th,
::v-deep .markdown-content table td {
  padding: 8px;
  border: 1px solid #ddd;
}

::v-deep .markdown-content table th {
  background-color: #f8f8f8;
  font-weight: bold;
}

::v-deep .markdown-content hr {
  height: 1px;
  border: none;
  background-color: #ddd;
  margin: 1em 0;
}
</style>

