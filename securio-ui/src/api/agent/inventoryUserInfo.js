import request from '@/utils/request'

// 查询存储用户信息列表
export function listInventoryUserInfo(query) {
  return request({
    url: '/agent/inventoryUserInfo/list',
    method: 'get',
    params: query
  })
}

// 查询存储用户信息详细
export function getInventoryUserInfo(id) {
  return request({
    url: '/agent/inventoryUserInfo/' + id,
    method: 'get'
  })
}

// 新增存储用户信息
export function addInventoryUserInfo(data) {
  return request({
    url: '/agent/inventoryUserInfo',
    method: 'post',
    data: data
  })
}

// 修改存储用户信息
export function updateInventoryUserInfo(data) {
  return request({
    url: '/agent/inventoryUserInfo',
    method: 'put',
    data: data
  })
}

// 删除存储用户信息
export function delInventoryUserInfo(id) {
  return request({
    url: '/agent/inventoryUserInfo/' + id,
    method: 'delete'
  })
}
