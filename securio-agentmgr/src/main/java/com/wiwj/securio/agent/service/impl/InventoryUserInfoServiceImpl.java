package com.wiwj.securio.agent.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.mapper.InventoryUserInfoMapper;
import com.wiwj.securio.agent.domain.InventoryUserInfo;
import com.wiwj.securio.agent.service.IInventoryUserInfoService;

/**
 * 存储用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class InventoryUserInfoServiceImpl implements IInventoryUserInfoService {
    @Autowired
    private InventoryUserInfoMapper inventoryUserInfoMapper;

    /**
     * 查询存储用户信息
     *
     * @param id 存储用户信息主键
     * @return 存储用户信息
     */
    @Override
    public InventoryUserInfo selectInventoryUserInfoById(Long id) {
        return inventoryUserInfoMapper.selectInventoryUserInfoById(id);
    }

    /**
     * 查询存储用户信息列表
     *
     * @param inventoryUserInfo 存储用户信息
     * @return 存储用户信息
     */
    @Override
    public List<InventoryUserInfo> selectInventoryUserInfoList(InventoryUserInfo inventoryUserInfo) {
        return inventoryUserInfoMapper.selectInventoryUserInfoList(inventoryUserInfo);
    }

    /**
     * 新增存储用户信息
     *
     * @param inventoryUserInfo 存储用户信息
     * @return 结果
     */
    @Override
    public int insertInventoryUserInfo(InventoryUserInfo inventoryUserInfo) {
        inventoryUserInfo.setIsDel(0);
        inventoryUserInfo.setCreateAt(System.currentTimeMillis());
        inventoryUserInfo.setUpdateAt(System.currentTimeMillis());
        return inventoryUserInfoMapper.insertInventoryUserInfo(inventoryUserInfo);
    }

    /**
     * 修改存储用户信息
     *
     * @param inventoryUserInfo 存储用户信息
     * @return 结果
     */
    @Override
    public int updateInventoryUserInfo(InventoryUserInfo inventoryUserInfo) {
        return inventoryUserInfoMapper.updateInventoryUserInfo(inventoryUserInfo);
    }

    /**
     * 批量删除存储用户信息
     *
     * @param ids 需要删除的存储用户信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryUserInfoByIds(Long[] ids) {
        return inventoryUserInfoMapper.deleteInventoryUserInfoByIds(ids);
    }

    /**
     * 删除存储用户信息信息
     *
     * @param id 存储用户信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryUserInfoById(Long id) {
        return inventoryUserInfoMapper.deleteInventoryUserInfoById(id);
    }

    @Override
    public int batchInsertInventoryUserInfo(List<InventoryUserInfo> inventoryUserInfoList) {
        return inventoryUserInfoMapper.batchInsertInventoryUserInfo(inventoryUserInfoList);
    }

    @Override
    public int deleteInventoryUserInfoByAgentId(String agentId) {
        return inventoryUserInfoMapper.deleteInventoryUserInfoByAgentId(agentId);
    }
}
