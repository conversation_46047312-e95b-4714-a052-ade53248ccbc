package com.wiwj.system.service;

import java.util.List;
import com.wiwj.system.domain.IndexCategoryItem;

/**
 * 索引条目信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public interface IIndexCategoryItemService {
    /**
     * 查询索引条目信息
     *
     * @param id 索引条目信息主键
     * @return 索引条目信息
     */
    public IndexCategoryItem selectIndexCategoryItemById(Long id);

    /**
     * 查询索引条目信息列表
     *
     * @param indexCategoryItem 索引条目信息
     * @return 索引条目信息集合
     */
    public List<IndexCategoryItem> selectIndexCategoryItemList(IndexCategoryItem indexCategoryItem);

    /**
     * 新增索引条目信息
     *
     * @param indexCategoryItem 索引条目信息
     * @return 结果
     */
    public int insertIndexCategoryItem(IndexCategoryItem indexCategoryItem);

    /**
     * 修改索引条目信息
     *
     * @param indexCategoryItem 索引条目信息
     * @return 结果
     */
    public int updateIndexCategoryItem(IndexCategoryItem indexCategoryItem);

    /**
     * 批量删除索引条目信息
     *
     * @param ids 需要删除的索引条目信息主键集合
     * @return 结果
     */
    public int deleteIndexCategoryItemByIds(Long[] ids);

    /**
     * 删除索引条目信息信息
     *
     * @param id 索引条目信息主键
     * @return 结果
     */
    public int deleteIndexCategoryItemById(Long id);
}
