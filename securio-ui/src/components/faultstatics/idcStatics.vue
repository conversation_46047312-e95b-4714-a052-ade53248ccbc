<template>
  <el-card>
<!--    <div slot="header">-->
<!--      <span>{{ title }}</span>-->
<!--    </div>-->
    <div ref="idcChart" style="width: 100%; height: 400px;"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'idcStatistic',
  props: {
    // 数据源（列表）
    data: {
      type: Array,
      required: true
    },
    // 统计名称
    title: {
      type: String,
      default: 'IDC故障统计'
    }
  },
  data() {
    return {
      idcChart: null // ECharts 实例
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.idcChart = echarts.init(this.$refs.idcChart);
      this.updateIdcChart();
    });
  },
  watch: {
    data: {
      handler() {
        if (this.idcChart) {
          this.updateIdcChart();
        }
      },
      deep: true
    }
  },
  beforeDestroy() {
    if (this.idcChart) {
      this.idcChart.dispose();
    }
  },
  methods: {
    // 更新图表
    updateIdcChart() {
      if (!this.data || this.data.length === 0) {
        console.warn("数据为空，无法渲染图表");
        return;
      }

      // 提取所有月份
      const months = this.data.map(monthData => `${monthData.year}年${monthData.month}月`);

      // 提取所有部门名称
      const idcs = new Set();
      this.data.forEach(monthData => {
        if (monthData.idcStaticsList && monthData.idcStaticsList.length > 0) {
          monthData.idcStaticsList.forEach(idc => {
            idcs.add(idc.idc);
          });
        }
      });

      // 如果没有部门数据，显示提示
      if (idcs.size === 0) {
        console.warn("没有部门数据，无法渲染图表");
        return;
      }

      // 为每个部门生成数据
      const seriesData = Array.from(idcs).map(idc => {
        const data = this.data.map(monthData => {
          if (monthData.idcStaticsList && monthData.idcStaticsList.length > 0) {
            const idcData = monthData.idcStaticsList.find(d => d.idc === idc);
            return idcData ? idcData.count : null; // 没有数据时返回 null
          }
          return null; // 没有数据时返回 null
        });
        return {
          name: idc,
          type: 'bar',
          data: data,
          label: {
            show: true, // 显示标签
            position: 'top', // 标签位置在柱状图顶部
            formatter: (params) => {
              // 仅在有数据时显示部门名称和故障次数
              return params.value !== null ? `${params.seriesName}: ${params.value}` : '';
            }
          }
        };
      });

      // ECharts 配置
      const option = {
        title: {
          text: this.title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: Array.from(idcs), // 部门名称
          bottom: 10
        },
        xAxis: {
          type: 'category',
          data: months, // X 轴是月份
          axisLabel: {
            rotate: 45 // 如果月份名称过长，可以旋转 45 度
          }
        },
        yAxis: {
          type: 'value',
          name: '故障次数'
        },
        series: seriesData // 部门数据
      };

      // 渲染图表
      this.idcChart.setOption(option);
    }
  }
};
</script>

<style scoped>
.el-card {
  margin: 20px;
}
</style>
