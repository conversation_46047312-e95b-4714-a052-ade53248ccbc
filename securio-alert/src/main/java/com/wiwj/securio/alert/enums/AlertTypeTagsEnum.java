package com.wiwj.securio.alert.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 不同告警类型的标签分类枚举
 * 根据告警类型定义常用的标签组合
 * 
 * <AUTHOR>
 */
public enum AlertTypeTagsEnum {

    // ==================== 基础设施监控告警 ====================
    
    /** 系统性能告警 */
    SYSTEM_PERFORMANCE("system_performance", "系统性能告警", Arrays.asList(
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.INSTANCE_ID,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.DATACENTER,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.THRESHOLD,
        AlertTagEnum.CURRENT_VALUE,
        AlertTagEnum.MONITOR_SYSTEM,
        AlertTagEnum.TEAM,
        AlertTagEnum.URGENCY
    )),

    /** 网络监控告警 */
    NETWORK_MONITORING("network_monitoring", "网络监控告警", Arrays.asList(
        AlertTagEnum.SOURCE_IP,
        AlertTagEnum.TARGET_IP,
        AlertTagEnum.PORT,
        AlertTagEnum.PROTOCOL,
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.DATACENTER,
        AlertTagEnum.MONITOR_SYSTEM,
        AlertTagEnum.TEAM,
        AlertTagEnum.IMPACT
    )),

    /** 存储监控告警 */
    STORAGE_MONITORING("storage_monitoring", "存储监控告警", Arrays.asList(
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.INSTANCE_ID,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.COMPONENT,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.THRESHOLD,
        AlertTagEnum.CURRENT_VALUE,
        AlertTagEnum.TEAM,
        AlertTagEnum.URGENCY
    )),

    // ==================== 应用监控告警 ====================

    /** 应用性能告警 */
    APPLICATION_PERFORMANCE("application_performance", "应用性能告警", Arrays.asList(
        AlertTagEnum.SERVICE,
        AlertTagEnum.APPLICATION,
        AlertTagEnum.VERSION,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.CONTAINER,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.THRESHOLD,
        AlertTagEnum.CURRENT_VALUE,
        AlertTagEnum.TEAM,
        AlertTagEnum.BUSINESS_LINE
    )),

    /** 微服务告警 */
    MICROSERVICE("microservice", "微服务告警", Arrays.asList(
        AlertTagEnum.SERVICE,
        AlertTagEnum.NAMESPACE,
        AlertTagEnum.POD,
        AlertTagEnum.CONTAINER,
        AlertTagEnum.VERSION,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.CLUSTER,
        AlertTagEnum.NODE,
        AlertTagEnum.TEAM,
        AlertTagEnum.PROJECT
    )),

    /** API监控告警 */
    API_MONITORING("api_monitoring", "API监控告警", Arrays.asList(
        AlertTagEnum.SERVICE,
        AlertTagEnum.URL_PATH,
        AlertTagEnum.DOMAIN,
        AlertTagEnum.PROTOCOL,
        AlertTagEnum.ERROR_CODE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.VERSION,
        AlertTagEnum.TEAM,
        AlertTagEnum.BUSINESS_LINE,
        AlertTagEnum.IMPACT
    )),

    // ==================== 数据库监控告警 ====================

    /** 数据库性能告警 */
    DATABASE_PERFORMANCE("database_performance", "数据库性能告警", Arrays.asList(
        AlertTagEnum.COMPONENT,
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.INSTANCE_ID,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.DATACENTER,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.THRESHOLD,
        AlertTagEnum.CURRENT_VALUE,
        AlertTagEnum.TEAM,
        AlertTagEnum.BUSINESS_LINE
    )),

    /** 数据库连接告警 */
    DATABASE_CONNECTION("database_connection", "数据库连接告警", Arrays.asList(
        AlertTagEnum.COMPONENT,
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.PORT,
        AlertTagEnum.SERVICE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.ERROR_CODE,
        AlertTagEnum.ERROR_MESSAGE,
        AlertTagEnum.TEAM,
        AlertTagEnum.IMPACT
    )),

    // ==================== 安全监控告警 ====================

    /** Web安全告警 */
    WEB_SECURITY("web_security", "Web安全告警", Arrays.asList(
        AlertTagEnum.ATTACK_TYPE,
        AlertTagEnum.SOURCE_IP,
        AlertTagEnum.TARGET_IP,
        AlertTagEnum.DOMAIN,
        AlertTagEnum.URL_PATH,
        AlertTagEnum.USER_AGENT,
        AlertTagEnum.SECURITY_RULE,
        AlertTagEnum.THREAT_LEVEL,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.TEAM
    )),

    /** 网络安全告警 */
    NETWORK_SECURITY("network_security", "网络安全告警", Arrays.asList(
        AlertTagEnum.ATTACK_TYPE,
        AlertTagEnum.SOURCE_IP,
        AlertTagEnum.TARGET_IP,
        AlertTagEnum.PORT,
        AlertTagEnum.PROTOCOL,
        AlertTagEnum.SECURITY_RULE,
        AlertTagEnum.THREAT_LEVEL,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.DATACENTER,
        AlertTagEnum.TEAM
    )),

    /** 主机安全告警 */
    HOST_SECURITY("host_security", "主机安全告警", Arrays.asList(
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.INSTANCE_ID,
        AlertTagEnum.ATTACK_TYPE,
        AlertTagEnum.ATTACKER_IP,
        AlertTagEnum.SECURITY_RULE,
        AlertTagEnum.THREAT_LEVEL,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.DATACENTER,
        AlertTagEnum.TEAM
    )),

    // ==================== 业务监控告警 ====================

    /** 业务指标告警 */
    BUSINESS_METRICS("business_metrics", "业务指标告警", Arrays.asList(
        AlertTagEnum.BUSINESS_LINE,
        AlertTagEnum.PRODUCT,
        AlertTagEnum.SERVICE,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.THRESHOLD,
        AlertTagEnum.CURRENT_VALUE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.TIME_WINDOW,
        AlertTagEnum.TEAM,
        AlertTagEnum.OWNER
    )),

    /** 用户体验告警 */
    USER_EXPERIENCE("user_experience", "用户体验告警", Arrays.asList(
        AlertTagEnum.SERVICE,
        AlertTagEnum.APPLICATION,
        AlertTagEnum.URL_PATH,
        AlertTagEnum.ERROR_CODE,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.REGION,
        AlertTagEnum.BUSINESS_LINE,
        AlertTagEnum.IMPACT,
        AlertTagEnum.TEAM
    )),

    // ==================== 容器和云原生告警 ====================

    /** Kubernetes告警 */
    KUBERNETES("kubernetes", "Kubernetes告警", Arrays.asList(
        AlertTagEnum.CLUSTER,
        AlertTagEnum.NAMESPACE,
        AlertTagEnum.POD,
        AlertTagEnum.CONTAINER,
        AlertTagEnum.NODE,
        AlertTagEnum.SERVICE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.VERSION,
        AlertTagEnum.TEAM,
        AlertTagEnum.PROJECT
    )),

    /** 容器监控告警 */
    CONTAINER_MONITORING("container_monitoring", "容器监控告警", Arrays.asList(
        AlertTagEnum.CONTAINER,
        AlertTagEnum.POD,
        AlertTagEnum.NODE,
        AlertTagEnum.NAMESPACE,
        AlertTagEnum.CLUSTER,
        AlertTagEnum.METRIC_NAME,
        AlertTagEnum.THRESHOLD,
        AlertTagEnum.CURRENT_VALUE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.TEAM
    )),

    // ==================== 日志和错误告警 ====================

    /** 错误日志告警 */
    ERROR_LOG("error_log", "错误日志告警", Arrays.asList(
        AlertTagEnum.SERVICE,
        AlertTagEnum.APPLICATION,
        AlertTagEnum.COMPONENT,
        AlertTagEnum.ERROR_CODE,
        AlertTagEnum.ERROR_MESSAGE,
        AlertTagEnum.EXCEPTION_TYPE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.TEAM,
        AlertTagEnum.FREQUENCY
    )),

    /** 异常监控告警 */
    EXCEPTION_MONITORING("exception_monitoring", "异常监控告警", Arrays.asList(
        AlertTagEnum.SERVICE,
        AlertTagEnum.APPLICATION,
        AlertTagEnum.MODULE,
        AlertTagEnum.EXCEPTION_TYPE,
        AlertTagEnum.ERROR_MESSAGE,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.VERSION,
        AlertTagEnum.FREQUENCY,
        AlertTagEnum.TEAM,
        AlertTagEnum.IMPACT
    )),

    // ==================== 通用告警 ====================

    /** 通用系统告警 */
    GENERIC_SYSTEM("generic_system", "通用系统告警", Arrays.asList(
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.HOSTNAME,
        AlertTagEnum.SERVICE,
        AlertTagEnum.COMPONENT,
        AlertTagEnum.TEAM,
        AlertTagEnum.URGENCY,
        AlertTagEnum.IMPACT,
        AlertTagEnum.MONITOR_SYSTEM
    )),

    /** 测试告警 */
    TEST_ALERT("test_alert", "测试告警", Arrays.asList(
        AlertTagEnum.TEST,
        AlertTagEnum.ENVIRONMENT,
        AlertTagEnum.SERVICE,
        AlertTagEnum.TEAM,
        AlertTagEnum.AUTOMATED
    ));

    private final String type;
    private final String description;
    private final List<AlertTagEnum> recommendedTags;

    AlertTypeTagsEnum(String type, String description, List<AlertTagEnum> recommendedTags) {
        this.type = type;
        this.description = description;
        this.recommendedTags = recommendedTags;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public List<AlertTagEnum> getRecommendedTags() {
        return recommendedTags;
    }

    /**
     * 获取推荐标签的key数组
     */
    public String[] getRecommendedTagKeys() {
        return recommendedTags.stream()
                .map(AlertTagEnum::getKey)
                .toArray(String[]::new);
    }

    /**
     * 根据类型获取枚举
     */
    public static AlertTypeTagsEnum getByType(String type) {
        for (AlertTypeTagsEnum alertType : values()) {
            if (alertType.getType().equals(type)) {
                return alertType;
            }
        }
        return null;
    }

    /**
     * 检查标签是否为该告警类型推荐的标签
     */
    public boolean isRecommendedTag(String tagKey) {
        return recommendedTags.stream()
                .anyMatch(tag -> tag.getKey().equals(tagKey));
    }

    /**
     * 检查标签是否为该告警类型推荐的标签
     */
    public boolean isRecommendedTag(AlertTagEnum tag) {
        return recommendedTags.contains(tag);
    }
}
