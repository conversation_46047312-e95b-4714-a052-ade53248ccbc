<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="98px">
      <el-form-item label="主机IP地址" prop="hostIp">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="请输入主机IP地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="本地端口号" prop="localPort">
        <el-input
          v-model="queryParams.localPort"
          placeholder="请输入本地端口号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="进程名称" prop="process">
        <el-input
          v-model="queryParams.process"
          placeholder="请输入进程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="portInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主机IP地址" align="center" prop="hostIp" />
      <el-table-column label="协议类型" align="center" prop="protocol" />
      <el-table-column label="本地IP地址" align="center" prop="localIp" />
      <el-table-column label="本地端口号" align="center" prop="localPort" />
      <el-table-column label="远程IP地址" align="center" prop="remoteIp" />
      <el-table-column label="远程端口号" align="center" prop="remotePort" />
      <el-table-column label="连接状态" align="center" prop="state" />
      <el-table-column label="进程ID" align="center" prop="pid" />
      <el-table-column label="进程名称" align="center" prop="process" />
      <el-table-column label="创建时间" align="center" prop="createAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.createAt) }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateAt" >
        <template slot-scope="scope">{{ parseTime(scope.row.updateAt) }}</template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listPortInfo, getPortInfo, delPortInfo, addPortInfo, updatePortInfo } from "@/api/agent/portInfo";

export default {
  name: "PortInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储Agent主机的端口使用信息表格数据
      portInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentId: null,
        protocol: null,
        localIp: null,
        localPort: null,
        remoteIp: null,
        remotePort: null,
        state: null,
        pid: null,
        process: null,
        createAt: null,
        isDel: null,
        updateAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        agentId: [
          { required: true, message: "关联的Agent唯一标识不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询存储Agent主机的端口使用信息列表 */
    getList() {
      this.loading = true;
      listPortInfo(this.queryParams).then(response => {
        this.portInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        agentId: null,
        protocol: null,
        localIp: null,
        localPort: null,
        remoteIp: null,
        remotePort: null,
        state: null,
        pid: null,
        process: null,
        createAt: null,
        isDel: null,
        updateAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储Agent主机的端口使用信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPortInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储Agent主机的端口使用信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePortInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPortInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除存储Agent主机的端口使用信息编号为"' + ids + '"的数据项？').then(function() {
        return delPortInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('agent/portInfo/export', {
        ...this.queryParams
      }, `portInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
