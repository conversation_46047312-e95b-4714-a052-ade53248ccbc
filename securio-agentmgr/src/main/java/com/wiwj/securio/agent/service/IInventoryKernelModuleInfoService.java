package com.wiwj.securio.agent.service;

import java.util.List;
import com.wiwj.securio.agent.domain.InventoryKernelModuleInfo;

/**
 * 存储内核模块信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface IInventoryKernelModuleInfoService {
    /**
     * 查询存储内核模块信息
     *
     * @param id 存储内核模块信息主键
     * @return 存储内核模块信息
     */
    public InventoryKernelModuleInfo selectInventoryKernelModuleInfoById(Long id);

    /**
     * 查询存储内核模块信息列表
     *
     * @param inventoryKernelModuleInfo 存储内核模块信息
     * @return 存储内核模块信息集合
     */
    public List<InventoryKernelModuleInfo> selectInventoryKernelModuleInfoList(InventoryKernelModuleInfo inventoryKernelModuleInfo);

    /**
     * 新增存储内核模块信息
     *
     * @param inventoryKernelModuleInfo 存储内核模块信息
     * @return 结果
     */
    public int insertInventoryKernelModuleInfo(InventoryKernelModuleInfo inventoryKernelModuleInfo);

    /**
     * 修改存储内核模块信息
     *
     * @param inventoryKernelModuleInfo 存储内核模块信息
     * @return 结果
     */
    public int updateInventoryKernelModuleInfo(InventoryKernelModuleInfo inventoryKernelModuleInfo);

    /**
     * 批量删除存储内核模块信息
     *
     * @param ids 需要删除的存储内核模块信息主键集合
     * @return 结果
     */
    public int deleteInventoryKernelModuleInfoByIds(Long[] ids);

    /**
     * 删除存储内核模块信息信息
     *
     * @param id 存储内核模块信息主键
     * @return 结果
     */
    public int deleteInventoryKernelModuleInfoById(Long id);

    /**
     * 批量新增存储Agent主机的内核模块信息
     *
     * @param inventoryKernelModuleInfoList 存储Agent主机的内核模块信息列表
     * @return 结果
     */
    public int batchInsertInventoryKernelModuleInfo(List<InventoryKernelModuleInfo> inventoryKernelModuleInfoList);

    /**
     * 根据Agent ID删除存储Agent主机的内核模块信息
     *
     * @param agentId Agent ID
     * @return 结果
     */
    public int deleteInventoryKernelModuleInfoByAgentId(String agentId);
}
