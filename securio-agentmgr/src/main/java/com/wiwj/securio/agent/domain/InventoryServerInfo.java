package com.wiwj.securio.agent.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 主机系统信息对象 inventory_server_info
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public class InventoryServerInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** agentId */
    @Excel(name = "agentId")
    private String agentId;

    /** 主机IP */
    @Excel(name = "主机IP")
    private String hostIp;

    /** 开机时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开机时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bootTime;

    /** 信息收集时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "信息收集时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date collectTime;

    /** 系统环境变量 */
    @Excel(name = "系统环境变量")
    private String sysEnv;

    /** hosts信息 */
    @Excel(name = "hosts信息")
    private String hosts;

    /** dns配置信息 */
    @Excel(name = "dns配置信息")
    private String dns;

    /** 防火墙配置信息 */
    @Excel(name = "防火墙配置信息")
    private String firewall;

    /** 系统探测类型 */
    @Excel(name = "系统探测类型")
    private String systemdDetect;

    /** 磁盘占用情况 */
    @Excel(name = "磁盘占用情况")
    private String filesystem;

    /** 系统基础信息 */
    @Excel(name = "系统基础信息")
    private String hostBaseInfo;

    /** 扩展信息 */
    @Excel(name = "扩展信息")
    private String hostExtInfo;

    /** 是否删除，0否1是 */
    @Excel(name = "是否删除，0否1是")
    private Integer isDel;

    /** 插入时间 */
    @Excel(name = "插入时间")
    private Long createAt;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private Long updateAt;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }
    public void setAgentId(String agentId){
        this.agentId = agentId;
    }

    public String getAgentId(){
        return agentId;
    }
    public void setHostIp(String hostIp){
        this.hostIp = hostIp;
    }

    public String getHostIp(){
        return hostIp;
    }
    public void setBootTime(Date bootTime){
        this.bootTime = bootTime;
    }

    public Date getBootTime(){
        return bootTime;
    }
    public void setCollectTime(Date collectTime){
        this.collectTime = collectTime;
    }

    public Date getCollectTime(){
        return collectTime;
    }
    public void setSysEnv(String sysEnv){
        this.sysEnv = sysEnv;
    }

    public String getSysEnv(){
        return sysEnv;
    }
    public void setHosts(String hosts){
        this.hosts = hosts;
    }

    public String getHosts(){
        return hosts;
    }
    public void setDns(String dns){
        this.dns = dns;
    }

    public String getDns(){
        return dns;
    }
    public void setFirewall(String firewall){
        this.firewall = firewall;
    }

    public String getFirewall(){
        return firewall;
    }
    public void setSystemdDetect(String systemdDetect){
        this.systemdDetect = systemdDetect;
    }

    public String getSystemdDetect(){
        return systemdDetect;
    }
    public void setFilesystem(String filesystem){
        this.filesystem = filesystem;
    }

    public String getFilesystem(){
        return filesystem;
    }
    public void setHostBaseInfo(String hostBaseInfo){
        this.hostBaseInfo = hostBaseInfo;
    }

    public String getHostBaseInfo(){
        return hostBaseInfo;
    }
    public void setHostExtInfo(String hostExtInfo){
        this.hostExtInfo = hostExtInfo;
    }

    public String getHostExtInfo(){
        return hostExtInfo;
    }
    public void setIsDel(Integer isDel){
        this.isDel = isDel;
    }

    public Integer getIsDel(){
        return isDel;
    }
    public void setCreateAt(Long createAt){
        this.createAt = createAt;
    }

    public Long getCreateAt(){
        return createAt;
    }
    public void setUpdateAt(Long updateAt){
        this.updateAt = updateAt;
    }

    public Long getUpdateAt(){
        return updateAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentId", getAgentId())
            .append("hostIp", getHostIp())
            .append("bootTime", getBootTime())
            .append("collectTime", getCollectTime())
            .append("sysEnv", getSysEnv())
            .append("hosts", getHosts())
            .append("dns", getDns())
            .append("firewall", getFirewall())
            .append("systemdDetect", getSystemdDetect())
            .append("filesystem", getFilesystem())
            .append("hostBaseInfo", getHostBaseInfo())
            .append("hostExtInfo", getHostExtInfo())
            .append("isDel", getIsDel())
            .append("createAt", getCreateAt())
            .append("updateAt", getUpdateAt())
            .toString();
    }
}
