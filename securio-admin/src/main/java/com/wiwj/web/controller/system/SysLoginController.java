package com.wiwj.web.controller.system;

import java.util.List;
import java.util.Set;

import com.wiwj.system.domain.vo.ApplicationVo;
import com.wiwj.system.domain.vo.RouterVo;
import com.wiwj.system.service.ISysApplicationService;
import com.wiwj.system.service.ISysRoleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.constant.Constants;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.core.domain.entity.SysMenu;
import com.wiwj.common.core.domain.entity.SysUser;
import com.wiwj.common.core.domain.model.LoginBody;
import com.wiwj.common.utils.SecurityUtils;
import com.wiwj.framework.web.service.SysLoginService;
import com.wiwj.framework.web.service.SysPermissionService;
import com.wiwj.system.service.ISysMenuService;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysApplicationService applicationService;

    @Autowired
    private ISysMenuService sysMenuService;

    @Autowired
    private ISysRoleService roleService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);

        //如果没有角色，默认赋值普通用户角色
        if(CollectionUtils.isEmpty(roles)){
            Long[] userIds = new Long[]{user.getUserId()};
            roleService.insertAuthUsers(2L,userIds);

            roles = permissionService.getRolePermission(user);
        }

        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);


        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        if(user.isAdmin()){
            ajax.put("applicationList", applicationService.selectAllSysApplications());
        }else {
            List<String> applicationCodes = sysMenuService.getApplicationCode(user.getUserId());
            if (CollectionUtils.isNotEmpty(applicationCodes)) {
                ajax.put("applicationList", applicationService.selectSysApplications(applicationCodes));
            }
        }
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        AjaxResult ajax =  AjaxResult.success();

        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);

        List<ApplicationVo> applicationVoList = applicationService.selectAllSysApplications();

        List<RouterVo> routerVos =  menuService.buildMenus(menus,null);
        routerVos = menuService.buildApplicationRoute(applicationVoList,routerVos);


        ajax.put("data", routerVos);
        ajax.put("applicationList", applicationVoList);

        return ajax;
    }
}
