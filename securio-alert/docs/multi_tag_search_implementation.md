# 多标签搜索实现方案

## 1. 前端多标签搜索格式

### 支持的分隔符
- `AND` - 逻辑与（所有条件都必须满足）
- `OR` - 逻辑或（任一条件满足即可）
- `,` - 逗号分隔（默认为 AND 逻辑）
- `;` - 分号分隔（默认为 AND 逻辑）

### 搜索示例
```javascript
// 单标签搜索
"environment:production"

// 多标签 AND 搜索
"environment:production AND service:user-api"
"environment:production, service:user-api"
"environment:production; service:user-api"

// 多标签 OR 搜索
"environment:production OR environment:staging"

// 复杂组合
"environment:production AND (service:user-api OR service:order-service)"
```

## 2. 后端解析逻辑

### Java 解析器实现
```java
@Component
public class MultiTagSearchParser {
    
    /**
     * 解析多标签搜索字符串
     */
    public TagSearchCondition parseTagSearch(String tagSearch, String searchType) {
        if (StringUtils.isEmpty(tagSearch)) {
            return null;
        }
        
        // 解析标签组合
        List<TagGroup> tagGroups = parseTagGroups(tagSearch);
        
        // 构建 SQL 条件
        return buildSqlCondition(tagGroups, searchType);
    }
    
    /**
     * 解析标签组
     */
    private List<TagGroup> parseTagGroups(String tagSearch) {
        List<TagGroup> groups = new ArrayList<>();
        
        // 先处理 OR 分组
        String[] orGroups = tagSearch.split("\\s+OR\\s+");
        
        for (String orGroup : orGroups) {
            TagGroup group = new TagGroup();
            group.setLogicType("OR");
            
            // 处理 AND 条件
            String[] andTags = orGroup.split("\\s*(?:AND|,|;)\\s*");
            
            for (String tagStr : andTags) {
                TagCondition tag = parseTag(tagStr.trim());
                if (tag != null) {
                    group.addTag(tag);
                }
            }
            
            if (!group.getTags().isEmpty()) {
                groups.add(group);
            }
        }
        
        return groups;
    }
    
    /**
     * 解析单个标签
     */
    private TagCondition parseTag(String tagStr) {
        String[] parts = tagStr.split(":", 2);
        if (parts.length != 2) {
            return null;
        }
        
        TagCondition tag = new TagCondition();
        tag.setKey(parts[0].trim());
        tag.setValue(parts[1].trim());
        return tag;
    }
    
    /**
     * 构建 SQL 条件
     */
    private TagSearchCondition buildSqlCondition(List<TagGroup> tagGroups, String searchType) {
        if (tagGroups.isEmpty()) {
            return null;
        }
        
        StringBuilder sql = new StringBuilder();
        List<String> conditions = new ArrayList<>();
        
        for (TagGroup group : tagGroups) {
            String groupCondition = buildGroupCondition(group, searchType);
            if (StringUtils.isNotEmpty(groupCondition)) {
                conditions.add(groupCondition);
            }
        }
        
        if (conditions.isEmpty()) {
            return null;
        }
        
        // 多个组之间用 OR 连接
        sql.append("(").append(String.join(" OR ", conditions)).append(")");
        
        TagSearchCondition condition = new TagSearchCondition();
        condition.setSqlCondition(sql.toString());
        return condition;
    }
    
    /**
     * 构建组条件
     */
    private String buildGroupCondition(TagGroup group, String searchType) {
        List<String> tagConditions = new ArrayList<>();
        
        for (TagCondition tag : group.getTags()) {
            String condition = buildTagCondition(tag, searchType);
            if (StringUtils.isNotEmpty(condition)) {
                tagConditions.add(condition);
            }
        }
        
        if (tagConditions.isEmpty()) {
            return null;
        }
        
        // 组内标签用 AND 连接
        return "(" + String.join(" AND ", tagConditions) + ")";
    }
    
    /**
     * 构建单个标签条件
     */
    private String buildTagCondition(TagCondition tag, String searchType) {
        String key = tag.getKey();
        String value = tag.getValue();
        
        if ("exact".equals(searchType)) {
            return String.format("JSON_EXTRACT(tags, '$.%s') = '%s'", key, value);
        } else {
            return String.format("JSON_EXTRACT(tags, '$.%s') LIKE '%%%s%%'", key, value);
        }
    }
}

// 数据模型
@Data
public class TagSearchCondition {
    private String sqlCondition;
}

@Data
public class TagGroup {
    private String logicType = "AND"; // AND | OR
    private List<TagCondition> tags = new ArrayList<>();
    
    public void addTag(TagCondition tag) {
        this.tags.add(tag);
    }
}

@Data
public class TagCondition {
    private String key;
    private String value;
}
```

## 3. MyBatis 动态 SQL

### Mapper XML 实现
```xml
<select id="selectAlertListWithMultiTags" parameterType="AlertQuery" resultMap="AlertResult">
    SELECT * FROM alert
    <where>
        is_del = 0
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND source_type = #{sourceType}
        </if>
        <if test="severity != null and severity != ''">
            AND severity = #{severity}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="serviceName != null and serviceName != ''">
            AND JSON_EXTRACT(tags, '$.service') LIKE CONCAT('%', #{serviceName}, '%')
        </if>
        <if test="sourceIp != null and sourceIp != ''">
            AND source_ip = #{sourceIp}
        </if>
        <if test="targetIp != null and targetIp != ''">
            AND target_ip = #{targetIp}
        </if>
        <!-- 多标签搜索条件 -->
        <if test="tagSearchCondition != null and tagSearchCondition != ''">
            AND ${tagSearchCondition}
        </if>
        <if test="startTime != null and endTime != null">
            AND occurred_at BETWEEN #{startTime} AND #{endTime}
        </if>
    </where>
    ORDER BY occurred_at DESC
</select>
```

## 4. Service 层实现

### AlertService 更新
```java
@Service
public class AlertServiceImpl implements IAlertService {
    
    @Autowired
    private MultiTagSearchParser tagSearchParser;
    
    @Override
    public List<Alert> selectAlertList(Alert alert) {
        // 处理多标签搜索
        if (StringUtils.isNotEmpty(alert.getTagSearch())) {
            TagSearchCondition condition = tagSearchParser.parseTagSearch(
                alert.getTagSearch(), 
                alert.getTagSearchType()
            );
            if (condition != null) {
                alert.setTagSearchCondition(condition.getSqlCondition());
            }
        }
        
        // 处理时间范围
        if (alert.getTimeRange() != null && alert.getTimeRange().length == 2) {
            alert.setStartTime(parseTime(alert.getTimeRange()[0]));
            alert.setEndTime(parseTime(alert.getTimeRange()[1]));
        }
        
        return alertMapper.selectAlertListWithMultiTags(alert);
    }
}
```

## 5. 实际 SQL 示例

### 单标签搜索
```sql
-- 输入: "environment:production"
SELECT * FROM alert 
WHERE JSON_EXTRACT(tags, '$.environment') LIKE '%production%'
  AND is_del = 0;
```

### 多标签 AND 搜索
```sql
-- 输入: "environment:production AND service:user-api"
SELECT * FROM alert 
WHERE (JSON_EXTRACT(tags, '$.environment') LIKE '%production%' 
       AND JSON_EXTRACT(tags, '$.service') LIKE '%user-api%')
  AND is_del = 0;
```

### 多标签 OR 搜索
```sql
-- 输入: "environment:production OR environment:staging"
SELECT * FROM alert 
WHERE (JSON_EXTRACT(tags, '$.environment') LIKE '%production%' 
       OR JSON_EXTRACT(tags, '$.environment') LIKE '%staging%')
  AND is_del = 0;
```

### 复杂组合搜索
```sql
-- 输入: "environment:production AND service:user-api OR service:order-service"
SELECT * FROM alert 
WHERE ((JSON_EXTRACT(tags, '$.environment') LIKE '%production%' 
        AND JSON_EXTRACT(tags, '$.service') LIKE '%user-api%') 
       OR (JSON_EXTRACT(tags, '$.service') LIKE '%order-service%'))
  AND is_del = 0;
```

## 6. 性能优化

### 虚拟列索引优化
```sql
-- 为常用标签创建虚拟列
ALTER TABLE alert ADD COLUMN environment_tag VARCHAR(50) 
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.environment'))) VIRTUAL;
CREATE INDEX idx_environment_tag ON alert(environment_tag);

ALTER TABLE alert ADD COLUMN service_tag VARCHAR(100) 
  GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(tags, '$.service'))) VIRTUAL;
CREATE INDEX idx_service_tag ON alert(service_tag);

-- 优化后的查询
SELECT * FROM alert 
WHERE environment_tag = 'production' 
  AND service_tag LIKE '%user-api%'
  AND is_del = 0;
```

### 查询缓存
```java
@Service
public class AlertSearchCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_PREFIX = "alert:search:";
    private static final int CACHE_EXPIRE = 300; // 5分钟
    
    public List<Alert> getCachedSearchResult(String cacheKey) {
        return (List<Alert>) redisTemplate.opsForValue().get(CACHE_PREFIX + cacheKey);
    }
    
    public void cacheSearchResult(String cacheKey, List<Alert> alerts) {
        redisTemplate.opsForValue().set(
            CACHE_PREFIX + cacheKey, 
            alerts, 
            CACHE_EXPIRE, 
            TimeUnit.SECONDS
        );
    }
    
    public String generateCacheKey(Alert alert) {
        return DigestUtils.md5Hex(JSON.toJSONString(alert));
    }
}
```

## 7. 前端使用示例

### 简单模式
```javascript
// 用户输入
queryParams.tagSearch = "environment:production AND service:user-api"

// 发送请求
this.getList()
```

### 高级模式
```javascript
// 用户选择多个标签
selectedAdvancedTags = [
  "environment:production",
  "service:user-api", 
  "team:backend"
]

// 组合成搜索字符串
queryParams.tagSearch = selectedAdvancedTags.join(' AND ')

// 发送请求
this.getList()
```

## 8. 错误处理

### 前端验证
```javascript
// 验证标签格式
validateTagFormat(tagSearch) {
  const tagPattern = /^[a-zA-Z_][a-zA-Z0-9_]*:[a-zA-Z0-9_\-\.]+$/
  const tags = tagSearch.split(/\s*(?:AND|OR|,|;)\s*/)
  
  for (let tag of tags) {
    if (!tagPattern.test(tag.trim())) {
      this.$message.error(`标签格式错误: ${tag}`)
      return false
    }
  }
  return true
}
```

### 后端异常处理
```java
@ControllerAdvice
public class TagSearchExceptionHandler {
    
    @ExceptionHandler(TagSearchException.class)
    public ResponseEntity<String> handleTagSearchException(TagSearchException e) {
        return ResponseEntity.badRequest().body("标签搜索格式错误: " + e.getMessage());
    }
}
```

这个多标签搜索方案既支持简单的文本输入，又提供了可视化的高级选择器，能够满足不同用户的使用习惯和需求。
