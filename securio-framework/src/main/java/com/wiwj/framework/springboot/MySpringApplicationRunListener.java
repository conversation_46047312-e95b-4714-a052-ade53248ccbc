package com.wiwj.framework.springboot;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringApplicationRunListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

public class MySpringApplicationRunListener implements SpringApplicationRunListener {
    Logger logger = LoggerFactory.getLogger(MySpringApplicationRunListener.class);

    private final SpringApplication application;

    private final String[] args;

    public MySpringApplicationRunListener(SpringApplication application, String[] args) {
        this.application = application;
        this.args = args;
    }

    @Override
    public void starting() {
        logger.info("starting");

    }

    @Override
    public void environmentPrepared(ConfigurableEnvironment environment) {
        //springboot 会在这个时机执行日志配置 org.springframework.boot.context.logging.LoggingApplicationListener
        logger.info("environmentPrepared");

        //处理配置文件
        FrameworkPropertiesSourceProcessor.processPropertySource(environment);
    }

    @Override
    public void contextPrepared(ConfigurableApplicationContext context) {
        //初始化日志的操作不能放到starting中，必须等其他自动加载日志的过程加载完之后再执行
        logger.info("contextPrepared");
    }

    @Override
    public void contextLoaded(ConfigurableApplicationContext context) {
        logger.info("contextLoaded");
    }

    @Override
    public void started(ConfigurableApplicationContext context) {
        logger.info("started");
    }

    @Override
    public void running(ConfigurableApplicationContext context) {
        logger.info("running");
    }

    @Override
    public void failed(ConfigurableApplicationContext context, Throwable exception) {
        logger.info("failed");
    }
}
