package com.wiwj.securio.agent.domain;

import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 存储内核模块信息对象 inventory_kernel_module_info
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public class InventoryKernelModuleInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键ID */
    private Long id;

    /** 模块名称 */
    @Excel(name = "模块名称")
    private String name;

    /** 关联的Agent唯一标识 */
    @Excel(name = "关联的Agent唯一标识")
    private String agentId;

    /** 模块路径 */
    @Excel(name = "模块路径")
    private String path;

    /** 模块大小（字节，若可用） */
    @Excel(name = "模块大小", readConverterExp = "字=节，若可用")
    private Long size;

    /** 是否加载（0:否, 1:是） */
    @Excel(name = "是否加载", readConverterExp = "0=:否,,1=:是")
    private Integer loaded;

    /** MD5哈希值 */
    @Excel(name = "MD5哈希值")
    private String md5;

    /** SHA-256哈希值 */
    @Excel(name = "SHA-256哈希值")
    private String sha256;

    /** 文件所有者（用户名） */
    @Excel(name = "文件所有者", readConverterExp = "用=户名")
    private String owner;

    /** 文件用户组（组名） */
    @Excel(name = "文件用户组", readConverterExp = "组=名")
    private String group;

    /** 文件权限（如rwxr-xr-x） */
    @Excel(name = "文件权限", readConverterExp = "如=rwxr-xr-x")
    private String permission;

    /** 最后更新时间 */
    @Excel(name = "最后更新时间")
    private String lastUpdate;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long createAt;

    /** 是否删除（0:否, 1:是） */
    @Excel(name = "是否删除", readConverterExp = "0=:否,,1=:是")
    private Integer isDel;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private Long updateAt;

    /** 主机IP */
    @Excel(name = "主机IP")
    private String hostIp;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setPath(String path){
        this.path = path;
    }

    public String getPath(){
        return path;
    }
    public void setSize(Long size){
        this.size = size;
    }

    public Long getSize(){
        return size;
    }
    public void setLoaded(Integer loaded){
        this.loaded = loaded;
    }

    public Integer getLoaded(){
        return loaded;
    }
    public void setMd5(String md5){
        this.md5 = md5;
    }

    public String getMd5(){
        return md5;
    }
    public void setSha256(String sha256){
        this.sha256 = sha256;
    }

    public String getSha256(){
        return sha256;
    }
    public void setOwner(String owner){
        this.owner = owner;
    }

    public String getOwner(){
        return owner;
    }
    public void setGroup(String group){
        this.group = group;
    }

    public String getGroup(){
        return group;
    }
    public void setPermission(String permission){
        this.permission = permission;
    }

    public String getPermission(){
        return permission;
    }
    public void setLastUpdate(String lastUpdate){
        this.lastUpdate = lastUpdate;
    }

    public String getLastUpdate(){
        return lastUpdate;
    }
    public void setCreateAt(Long createAt){
        this.createAt = createAt;
    }

    public Long getCreateAt(){
        return createAt;
    }
    public void setIsDel(Integer isDel){
        this.isDel = isDel;
    }

    public Integer getIsDel(){
        return isDel;
    }
    public void setUpdateAt(Long updateAt){
        this.updateAt = updateAt;
    }

    public Long getUpdateAt(){
        return updateAt;
    }
    public void setHostIp(String hostIp){
        this.hostIp = hostIp;
    }

    public String getHostIp(){
        return hostIp;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentId() {
        return agentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("path", getPath())
            .append("size", getSize())
            .append("loaded", getLoaded())
            .append("md5", getMd5())
            .append("sha256", getSha256())
            .append("owner", getOwner())
            .append("group", getGroup())
            .append("permission", getPermission())
            .append("lastUpdate", getLastUpdate())
            .append("createAt", getCreateAt())
            .append("isDel", getIsDel())
            .append("updateAt", getUpdateAt())
            .append("hostIp", getHostIp())
            .append("agentId", getAgentId())
            .toString();
    }
}
