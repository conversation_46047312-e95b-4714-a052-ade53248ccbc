package com.wiwj.securio.agent.mapper;

import java.util.List;
import com.wiwj.securio.agent.domain.InventoryServerInfo;

/**
 * 主机系统信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface InventoryServerInfoMapper {
    /**
     * 查询主机系统信息
     *
     * @param id 主机系统信息主键
     * @return 主机系统信息
     */
    public InventoryServerInfo selectInventoryServerInfoById(Long id);

    /**
     * 查询主机系统信息列表
     *
     * @param inventoryServerInfo 主机系统信息
     * @return 主机系统信息集合
     */
    public List<InventoryServerInfo> selectInventoryServerInfoList(InventoryServerInfo inventoryServerInfo);

    /**
     * 新增主机系统信息
     *
     * @param inventoryServerInfo 主机系统信息
     * @return 结果
     */
    public int insertInventoryServerInfo(InventoryServerInfo inventoryServerInfo);

    /**
     * 修改主机系统信息
     *
     * @param inventoryServerInfo 主机系统信息
     * @return 结果
     */
    public int updateInventoryServerInfo(InventoryServerInfo inventoryServerInfo);

    /**
     * 删除主机系统信息
     *
     * @param id 主机系统信息主键
     * @return 结果
     */
    public int deleteInventoryServerInfoById(Long id);

    /**
     * 批量删除主机系统信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryServerInfoByIds(Long[] ids);
}
