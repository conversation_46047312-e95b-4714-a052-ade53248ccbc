package com.wiwj.system.service;

import java.util.List;
import com.wiwj.system.domain.SysApplication;
import com.wiwj.system.domain.vo.ApplicationVo;

/**
 * 系统应用模块Service接口
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
public interface ISysApplicationService {
    /**
     * 查询系统应用模块
     *
     * @param id 系统应用模块主键
     * @return 系统应用模块
     */
    public SysApplication selectSysApplicationById(Long id);

    /**
     * 查询系统应用模块列表
     *
     * @param sysApplication 系统应用模块
     * @return 系统应用模块集合
     */
    public List<SysApplication> selectSysApplicationList(SysApplication sysApplication);

    /**
     * 新增系统应用模块
     *
     * @param sysApplication 系统应用模块
     * @return 结果
     */
    public int insertSysApplication(SysApplication sysApplication);

    /**
     * 修改系统应用模块
     *
     * @param sysApplication 系统应用模块
     * @return 结果
     */
    public int updateSysApplication(SysApplication sysApplication);

    /**
     * 批量删除系统应用模块
     *
     * @param ids 需要删除的系统应用模块主键集合
     * @return 结果
     */
    public int deleteSysApplicationByIds(Long[] ids);

    /**
     * 删除系统应用模块信息
     *
     * @param id 系统应用模块主键
     * @return 结果
     */
    public int deleteSysApplicationById(Long id);

    List<ApplicationVo> selectSysApplications(List<String> applicationCodes);

    List<ApplicationVo> selectAllSysApplications();

}
