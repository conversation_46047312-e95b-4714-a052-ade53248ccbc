package com.wiwj.securio.alert.service;

import java.util.List;
import java.util.Map;
import com.wiwj.securio.alert.domain.Alert;

/**
 * 告警Service接口
 *
 * <AUTHOR>
 */
public interface IAlertService
{
    /**
     * 查询告警
     *
     * @param id 告警ID
     * @return 告警
     */
    public Alert selectAlertById(Long id);

    /**
     * 查询告警列表
     *
     * @param alert 告警
     * @return 告警集合
     */
    public List<Alert> selectAlertList(Alert alert);

    /**
     * 新增告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int insertAlert(Alert alert);

    /**
     * 修改告警
     *
     * @param alert 告警
     * @return 结果
     */
    public int updateAlert(Alert alert);

    /**
     * 批量删除告警
     *
     * @param ids 需要删除的告警ID
     * @return 结果
     */
    public int deleteAlertByIds(Long[] ids);

    /**
     * 删除告警信息
     *
     * @param id 告警ID
     * @return 结果
     */
    public int deleteAlertById(Long id);

    /**
     * 批量逻辑删除告警
     *
     * @param ids 需要逻辑删除的告警ID
     * @return 结果
     */
    public int logicDeleteAlertByIds(Long[] ids);

    /**
     * 逻辑删除告警信息
     *
     * @param id 告警ID
     * @return 结果
     */
    public int logicDeleteAlertById(Long id);

    /**
     * 处理告警
     *
     * @param id 告警ID
     * @param resolvedBy 处理人ID
     * @param resolvedByName 处理人名称
     * @param resolutionNote 处理说明
     * @return 结果
     */
    public int resolveAlert(Long id, String resolvedBy, String resolvedByName, String resolutionNote);

    /**
     * 忽略告警
     *
     * @param id 告警ID
     * @param resolvedBy 处理人ID
     * @param resolvedByName 处理人名称
     * @param resolutionNote 处理说明
     * @return 结果
     */
    public int ignoreAlert(Long id, String resolvedBy, String resolvedByName, String resolutionNote);

    /**
     * 处理Webhook推送的告警数据
     *
     * @param token 推送URL令牌
     * @param jsonData JSON数据
     * @return 处理结果数据
     */
    public Map<String, Object> processWebhookAlert(String token, String jsonData) throws Exception;

    /**
     * 接收单个告警
     *
     * @param alert 告警对象
     * @return 处理结果数据
     */
    public Map<String, Object> receiveAlert(Alert alert) throws Exception;

    /**
     * 批量接收告警
     *
     * @param alerts 告警对象数组
     * @return 处理结果数据
     */
    public Map<String, Object> receiveBatchAlerts(Alert[] alerts) throws Exception;

    /**
     * 获取告警统计数据
     *
     * @return 统计数据
     */
    public Map<String, Object> getAlertStatistics();

    /**
     * 获取告警趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getAlertTrend(Integer days);

    /**
     * 获取告警分布数据
     *
     * @param type 分布类型（severity, status, sourceType等）
     * @return 分布数据
     */
    public List<Map<String, Object>> getAlertDistribution(String type);

    /**
     * 获取热点告警源
     *
     * @param limit 返回数量限制
     * @return 热点告警源列表
     */
    public List<Map<String, Object>> getTopAlertSources(Integer limit);

    /**
     * 获取告警处理统计
     *
     * @param days 统计天数
     * @return 处理统计数据
     */
    public Map<String, Object> getResolutionStatistics(Integer days);
}
