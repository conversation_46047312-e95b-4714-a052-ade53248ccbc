package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.PatchSuperseded;
import com.wiwj.securio.intelmgr.service.IPatchSupersededService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 被替代的补丁Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patchSuperseded")
public class PatchSupersededController extends BaseController {
    @Autowired
    private IPatchSupersededService patchSupersededService;

    /**
     * 查询被替代的补丁列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patchSuperseded:list')")
    @GetMapping("/list")
    public TableDataInfo list(PatchSuperseded patchSuperseded) {
        startPage();
        List<PatchSuperseded> list = patchSupersededService.selectPatchSupersededList(patchSuperseded);
        return getDataTable(list);
    }

    /**
     * 导出被替代的补丁列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patchSuperseded:export')")
    @Log(title = "被替代的补丁", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatchSuperseded patchSuperseded) {
        List<PatchSuperseded> list = patchSupersededService.selectPatchSupersededList(patchSuperseded);
        ExcelUtil<PatchSuperseded> util = new ExcelUtil<PatchSuperseded>(PatchSuperseded.class);
        util.exportExcel(response, list, "被替代的补丁数据");
    }

    /**
     * 获取被替代的补丁详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patchSuperseded:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(patchSupersededService.selectPatchSupersededById(id));
    }

    /**
     * 新增被替代的补丁
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patchSuperseded:add')")
    @Log(title = "被替代的补丁", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatchSuperseded patchSuperseded) {
        return toAjax(patchSupersededService.insertPatchSuperseded(patchSuperseded));
    }

    /**
     * 修改被替代的补丁
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patchSuperseded:edit')")
    @Log(title = "被替代的补丁", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatchSuperseded patchSuperseded) {
        return toAjax(patchSupersededService.updatePatchSuperseded(patchSuperseded));
    }

    /**
     * 删除被替代的补丁
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patchSuperseded:remove')")
    @Log(title = "被替代的补丁", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patchSupersededService.deletePatchSupersededByIds(ids));
    }
}
