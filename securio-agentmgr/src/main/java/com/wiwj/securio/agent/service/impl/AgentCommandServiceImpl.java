package com.wiwj.securio.agent.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.mapper.AgentCommandMapper;
import com.wiwj.securio.agent.domain.AgentCommand;
import com.wiwj.securio.agent.service.IAgentCommandService;

import cn.hutool.core.lang.UUID;

/**
 * 探针命令记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Service
public class AgentCommandServiceImpl implements IAgentCommandService {
    private static final Logger logger = LoggerFactory.getLogger(AgentCommandServiceImpl.class);

    @Autowired
    private AgentCommandMapper agentCommandMapper;

    /**
     * 查询探针命令记录
     *
     * @param id 探针命令记录主键
     * @return 探针命令记录
     */
    @Override
    public AgentCommand selectAgentCommandById(Long id) {
        return agentCommandMapper.selectAgentCommandById(id);
    }

    /**
     * 查询探针命令记录列表
     *
     * @param agentCommand 探针命令记录
     * @return 探针命令记录
     */
    @Override
    public List<AgentCommand> selectAgentCommandList(AgentCommand agentCommand) {
        return agentCommandMapper.selectAgentCommandList(agentCommand);
    }

    /**
     * 新增探针命令记录
     *
     * @param agentCommand 探针命令记录
     * @return 结果
     */
    @Override
    public int insertAgentCommand(AgentCommand agentCommand) {
        agentCommand.setStatus("pending");
        agentCommand.setCommandId(UUID.randomUUID().toString());
        agentCommand.setCreatedAt(new Date());
        agentCommand.setUpdatedAt(new Date());
        return agentCommandMapper.insertAgentCommand(agentCommand);
    }

    /**
     * 修改探针命令记录
     *
     * @param agentCommand 探针命令记录
     * @return 结果
     */
    @Override
    public int updateAgentCommand(AgentCommand agentCommand) {
        agentCommand.setUpdatedAt(new Date());
        return agentCommandMapper.updateAgentCommand(agentCommand);
    }

    /**
     * 批量删除探针命令记录
     *
     * @param ids 需要删除的探针命令记录主键
     * @return 结果
     */
    @Override
    public int deleteAgentCommandByIds(Long[] ids) {
        return agentCommandMapper.deleteAgentCommandByIds(ids);
    }

    /**
     * 删除探针命令记录信息
     *
     * @param id 探针命令记录主键
     * @return 结果
     */
    @Override
    public int deleteAgentCommandById(Long id) {
        return agentCommandMapper.deleteAgentCommandById(id);
    }

    /**
     * 获取Agent待执行的命令列表
     *
     * @param agentUuid Agent唯一标识
     * @return 待执行的命令列表
     */
    @Override
    public List<AgentCommand> getPendingCommands(String agentUuid) {
        if (agentUuid == null || agentUuid.isEmpty()) {
            logger.warn("Cannot get pending commands for null or empty agent UUID");
            return new ArrayList<>();
        }

        AgentCommand query = new AgentCommand();
        query.setAgentUuid(agentUuid);
        query.setStatus("pending");

        List<AgentCommand> pendingCommands = agentCommandMapper.selectAgentCommandList(query);
        logger.info("Found {} pending commands for agent: {}",
                pendingCommands != null ? pendingCommands.size() : 0, agentUuid);

        return pendingCommands != null ? pendingCommands : new ArrayList<>();
    }

    /**
     * 标记命令为已接收状态
     *
     * @param commandIds 命令ID列表
     * @return 结果
     */
    @Override
    public int markCommandsAsReceived(List<String> commandIds) {
        if (commandIds == null || commandIds.isEmpty()) {
            return 0;
        }

        int updatedCount = 0;
        for (String commandId : commandIds) {
            AgentCommand query = new AgentCommand();
            query.setCommandId(commandId);
            List<AgentCommand> commands = agentCommandMapper.selectAgentCommandList(query);

            if (commands != null && !commands.isEmpty()) {
                AgentCommand command = commands.get(0);
                command.setStatus("received");
                command.setReceivedAt(new Date());
                command.setUpdatedAt(new Date());

                int result = agentCommandMapper.updateAgentCommand(command);
                updatedCount += result;

                logger.info("Marked command {} as received, result: {}", commandId, result);
            } else {
                logger.warn("Command not found for ID: {}", commandId);
            }
        }

        return updatedCount;
    }
}
