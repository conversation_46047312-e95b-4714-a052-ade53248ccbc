package com.wiwj.securio.logmgr;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * VictoriaLogs API 测试类
 *
 * 注意：这些测试需要 VictoriaLogs 服务正在运行
 * 如果 VictoriaLogs 服务未运行，测试将失败
 */
@SpringBootTest(classes = TestApplication.class)
public class VictoriaLogsApiTest {

    @Value("${victoria.logs.url:http://0.0.0.0:8428}")
    private String victoriaLogsUrl;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 测试查询日志 API
     */
    @Test
    public void testQueryLogs() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "error");
        params.add("limit", "5");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/query";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志命中统计 API
     */
    @Test
    public void testQueryHits() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "error");
        params.add("start", "1h");
        params.add("step", "10m");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/hits";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志命中统计 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志字段频率统计 API
     */
    @Test
    public void testQueryFacets() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");
        params.add("limit", "3");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/facets";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志字段频率统计 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志统计 API
     */
    @Test
    public void testQueryStats() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "* | stats by (level) count(*)");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/stats_query";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志统计 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志范围统计 API
     */
    @Test
    public void testQueryStatsRange() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "* | stats by (level) count(*)");
        params.add("start", "1h");
        params.add("step", "10m");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/stats_query_range";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志范围统计 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志流 ID API
     */
    @Test
    public void testQueryStreamIds() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");
        params.add("limit", "3");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/stream_ids";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志流 ID API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志流 API
     */
    @Test
    public void testQueryStreams() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");
        params.add("limit", "3");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/streams";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志流 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志流字段名 API
     */
    @Test
    public void testQueryStreamFieldNames() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/stream_field_names";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志流字段名 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志流字段值 API
     */
    @Test
    public void testQueryStreamFieldValues() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");
        params.add("field", "host");
        params.add("limit", "3");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/stream_field_values";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志流字段值 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志字段名 API
     */
    @Test
    public void testQueryFieldNames() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/field_names";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志字段名 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试查询日志字段值 API
     */
    @Test
    public void testQueryFieldValues() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "*");
        params.add("start", "1h");
        params.add("field", "level");
        params.add("limit", "3");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/field_values";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("查询日志字段值 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 测试使用额外过滤条件查询日志
     */
    @Test
    public void testQueryLogsWithExtraFilters() throws Exception {
        Map<String, Object> extraFilters = new HashMap<>();
        extraFilters.put("level", "error");

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("query", "error");
        params.add("limit", "5");
        params.add("extra_filters", JSON.toJSONString(extraFilters));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        String url = victoriaLogsUrl + "/select/logsql/query";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        System.out.println("使用额外过滤条件查询日志 API 响应: " + response.getBody());

        assert response.getStatusCode().is2xxSuccessful();
    }
}
