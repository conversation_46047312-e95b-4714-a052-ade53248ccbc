package com.wiwj.securio.logmgr.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.domain.dto.FieldStatDTO;
import com.wiwj.securio.logmgr.domain.dto.LogFieldStatDTO;
import com.wiwj.securio.logmgr.domain.dto.LogFieldStatsRequestDTO;
import com.wiwj.securio.logmgr.domain.dto.LogFieldStatsResponseDTO;
import com.wiwj.securio.logmgr.service.VictoriaLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * VictoriaLogs 控制器
 *
 * <AUTHOR>
 */
@Api(tags = "日志查询管理", description = "VictoriaLogs 日志查询相关接口")
@RestController
@RequestMapping("/opslog/victoria")
public class VictoriaLogsController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(VictoriaLogsController.class);

    @Autowired
    private VictoriaLogsService victoriaLogsService;

    /**
     * 查询日志
     */
    @ApiOperation(value = "查询日志", notes = "根据查询条件查询日志")
    @PostMapping("/query")
    public AjaxResult queryLogs(
            @ApiParam(value = "查询语句", required = true) @RequestParam String query,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String start,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String end,
            @ApiParam(value = "限制返回的日志条数") @RequestParam(required = false) Integer limit,
            @ApiParam(value = "查询超时时间") @RequestParam(required = false) String timeout,
            @ApiParam(value = "额外过滤条件") @RequestBody(required = false) Map<String, Object> extraFilters,
            @ApiParam(value = "额外流过滤条件") @RequestBody(required = false) Map<String, Object> extraStreamFilters) {

        String result = victoriaLogsService.queryLogs(query, start, end, limit, timeout, extraFilters, extraStreamFilters);
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("data", result);
        return ajaxResult;
    }

    /**
     * 获取字段统计信息
     */
    @ApiOperation(value = "获取字段统计信息", notes = "获取日志字段的统计信息，包括字段值分布")
    @PostMapping("/facets")
    public AjaxResult facets(@RequestBody LogFieldStatsRequestDTO requestDTO) {
        try {
            // 构建查询参数
            String query = requestDTO.getQuery();
            String timeRange = requestDTO.getTimeRange();
            List<String> fields = requestDTO.getFields();
            Integer limit = requestDTO.getLimit() != null ? requestDTO.getLimit() : 10;

            // 如果提供了时间范围，添加到查询条件中
            if (timeRange != null && !timeRange.isEmpty() && !query.contains("_time:")) {
                query = "_time:" + timeRange + " and " + query;
            }

            logger.info("获取字段统计信息: query={}, fields={}, limit={}", query, fields, limit);

            // 获取原始的 JSON 字符串结果
            String jsonResult = victoriaLogsService.facets(query, limit);

            // 使用 fastjson 将 JSON 字符串转换为 LogFieldStatsResponseDTO 对象
            // 配置解析选项，支持下划线和驼峰命名的自动转换
            JSONReader.Feature[] features = {
                JSONReader.Feature.SupportSmartMatch
            };

            LogFieldStatsResponseDTO responseDTO =
                JSON.parseObject(jsonResult, LogFieldStatsResponseDTO.class, features);

            // 如果指定了字段列表，过滤出指定的字段
            if (fields != null && !fields.isEmpty() && responseDTO != null && responseDTO.getFacets() != null) {
                List<LogFieldStatDTO> filteredStats = responseDTO.getFacets().stream()
                    .filter(stat -> fields.contains(stat.getFieldName()))
                    .collect(Collectors.toList());
                responseDTO.setFacets(filteredStats);
            }
            return AjaxResult.success(responseDTO);
        } catch (Exception e) {
            logger.error("获取字段统计信息失败: " + e.getMessage(), e);
            return AjaxResult.error("获取字段统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 按字段统计
     */
    @ApiOperation(value = "按字段统计", notes = "按指定字段统计日志数量并返回 DTO 列表")
    @GetMapping("/statByField")
    public AjaxResult statByField(
            @ApiParam(value = "日志流名称", required = true) @RequestParam String stream,
            @ApiParam(value = "统计字段", required = true) @RequestParam String field,
            @ApiParam(value = "时间范围", example = "7d") @RequestParam(required = false, defaultValue = "7d") String timeRange,
            @ApiParam(value = "限制返回的结果数量", example = "20") @RequestParam(required = false, defaultValue = "20") Integer limit) {

        try {
            logger.info("按字段统计: stream={}, field={}, timeRange={}, limit={}", stream, field, timeRange, limit);

            // 调用服务方法获取统计结果
            List<FieldStatDTO> stats = victoriaLogsService.statByField(stream, field, timeRange, limit);

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("按字段统计失败: " + e.getMessage(), e);
            return AjaxResult.error("按字段统计失败: " + e.getMessage());
        }
    }

    /**
     * 查询时间序列统计数据
     */
    @ApiOperation(value = "查询时间序列统计数据", notes = "根据查询条件查询时间序列统计数据")
    @PostMapping("/stats_query_range")
    public AjaxResult queryStatsRange(
            @ApiParam(value = "查询语句", required = true) @RequestParam String query,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String start,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String end,
            @ApiParam(value = "时间步长", example = "1h") @RequestParam(required = false, defaultValue = "1h") String step,
            @ApiParam(value = "额外过滤条件") @RequestBody(required = false) Map<String, Object> extraFilters,
            @ApiParam(value = "额外流过滤条件") @RequestBody(required = false) Map<String, Object> extraStreamFilters) {

        try {
            logger.info("查询时间序列统计数据: query={}, start={}, end={}, step={}", query, start, end, step);

            // 调用服务方法获取时间序列统计数据
            String result = victoriaLogsService.queryStatsRange(query, start, end, step, extraFilters, extraStreamFilters);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("查询时间序列统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("查询时间序列统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定 stream 指定字段去重后的统计数量
     */
    @ApiOperation(value = "获取指定字段去重后的统计数量", notes = "获取指定 stream 指定字段去重后的统计数量")
    @GetMapping("/count_unique")
    public AjaxResult countUniqueValues(
            @ApiParam(value = "日志流名称", required = true) @RequestParam String stream,
            @ApiParam(value = "需要去重的字段", required = true) @RequestParam String field,
            @ApiParam(value = "时间范围", example = "7d") @RequestParam(required = false, defaultValue = "7d") String timeRange) {

        try {
            logger.info("获取指定字段去重后的统计数量: stream={}, field={}, timeRange={}", stream, field, timeRange);

            // 调用服务方法获取去重后的统计数量
            int count = victoriaLogsService.countUniqueValues(stream, field, timeRange);

            return AjaxResult.success(count);
        } catch (Exception e) {
            logger.error("获取指定字段去重后的统计数量失败: " + e.getMessage(), e);
            return AjaxResult.error("获取指定字段去重后的统计数量失败: " + e.getMessage());
        }
    }
}
