package com.wiwj.securio.agent.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.agent.domain.InventoryServerInfo;
import com.wiwj.securio.agent.service.IInventoryServerInfoService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;

/**
 * 主机系统信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/agent/serverInfo")
public class InventoryServerInfoController extends BaseController {
    @Autowired
    private IInventoryServerInfoService inventoryServerInfoService;

    /**
     * 查询主机系统信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:serverInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryServerInfo inventoryServerInfo) {
        startPage();
        List<InventoryServerInfo> list = inventoryServerInfoService.selectInventoryServerInfoList(inventoryServerInfo);
        return getDataTable(list);
    }

    /**
     * 导出主机系统信息列表
     */
    @PreAuthorize("@ss.hasPermi('agent:serverInfo:export')")
    @Log(title = "主机系统信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryServerInfo inventoryServerInfo) {
        List<InventoryServerInfo> list = inventoryServerInfoService.selectInventoryServerInfoList(inventoryServerInfo);
        ExcelUtil<InventoryServerInfo> util = new ExcelUtil<InventoryServerInfo>(InventoryServerInfo.class);
        util.exportExcel(response, list, "主机系统信息数据");
    }

    /**
     * 获取主机系统信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('agent:serverInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(inventoryServerInfoService.selectInventoryServerInfoById(id));
    }

    /**
     * 新增主机系统信息
     */
    @PreAuthorize("@ss.hasPermi('agent:serverInfo:add')")
    @Log(title = "主机系统信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryServerInfo inventoryServerInfo) {
        return toAjax(inventoryServerInfoService.insertInventoryServerInfo(inventoryServerInfo));
    }

    /**
     * 修改主机系统信息
     */
    @PreAuthorize("@ss.hasPermi('agent:serverInfo:edit')")
    @Log(title = "主机系统信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryServerInfo inventoryServerInfo) {
        return toAjax(inventoryServerInfoService.updateInventoryServerInfo(inventoryServerInfo));
    }

    /**
     * 删除主机系统信息
     */
    @PreAuthorize("@ss.hasPermi('agent:serverInfo:remove')")
    @Log(title = "主机系统信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(inventoryServerInfoService.deleteInventoryServerInfoByIds(ids));
    }
}
