package com.wiwj.securio.agent.mapper;

import java.util.List;
import com.wiwj.securio.agent.domain.InventoryPortInfo;

/**
 * 存储Agent主机的端口使用信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface InventoryPortInfoMapper {
    /**
     * 查询存储Agent主机的端口使用信息
     *
     * @param id 存储Agent主机的端口使用信息主键
     * @return 存储Agent主机的端口使用信息
     */
    public InventoryPortInfo selectInventoryPortInfoById(Long id);

    /**
     * 查询存储Agent主机的端口使用信息列表
     *
     * @param inventoryPortInfo 存储Agent主机的端口使用信息
     * @return 存储Agent主机的端口使用信息集合
     */
    public List<InventoryPortInfo> selectInventoryPortInfoList(InventoryPortInfo inventoryPortInfo);

    /**
     * 新增存储Agent主机的端口使用信息
     *
     * @param inventoryPortInfo 存储Agent主机的端口使用信息
     * @return 结果
     */
    public int insertInventoryPortInfo(InventoryPortInfo inventoryPortInfo);

    /**
     * 修改存储Agent主机的端口使用信息
     *
     * @param inventoryPortInfo 存储Agent主机的端口使用信息
     * @return 结果
     */
    public int updateInventoryPortInfo(InventoryPortInfo inventoryPortInfo);

    /**
     * 删除存储Agent主机的端口使用信息
     *
     * @param id 存储Agent主机的端口使用信息主键
     * @return 结果
     */
    public int deleteInventoryPortInfoById(Long id);

    /**
     * 批量删除存储Agent主机的端口使用信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryPortInfoByIds(Long[] ids);

    int batchInsertInventoryPortInfo(List<InventoryPortInfo> inventoryPortInfoList);

    int deleteInventoryPortInfoByAgentId(String agentId);
}
