<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.alert.mapper.AlertMapper">

    <resultMap type="com.wiwj.securio.alert.domain.Alert" id="AlertResult">
        <result property="id"                column="id"                />
        <result property="title"             column="title"             />
        <result property="description"       column="description"       />
        <result property="severity"          column="severity"          />
        <result property="status"            column="status"            />
        <result property="alertType"         column="alert_type"        />
        <result property="sourceIp"          column="source_ip"         />
        <result property="targetIp"          column="target_ip"         />
        <result property="url"               column="url"               />
        <result property="eventId"           column="event_id"          />
        <result property="sourceType"        column="source_type"       />
        <result property="sourceSubType"     column="source_sub_type"   />
        <result property="sourceIdent"       column="source_ident"      />
        <result property="sourceName"        column="source_name"       />
        <result property="ruleName"          column="rule_name"         />
        <result property="groupName"         column="group_name"        />
        <result property="occurredAt"        column="occurred_at"       />
        <result property="detectedAt"        column="detected_at"       />
        <result property="resolvedAt"        column="resolved_at"       />
        <result property="resolvedBy"        column="resolved_by"       />
        <result property="resolvedByName"    column="resolved_by_name"  />
        <result property="resolutionNote"    column="resolution_note"   />
        <result property="durationMs"        column="duration_ms"       />
        <result property="tags"              column="tags"              />
        <result property="attributes"        column="attributes"        />
        <result property="rawData"           column="raw_data"          />
        <result property="createAt"          column="create_at"         />
        <result property="createBy"          column="create_by"         />
        <result property="createName"        column="create_name"       />
        <result property="updateAt"          column="update_at"         />
        <result property="updateBy"          column="update_by"         />
        <result property="updateName"        column="update_name"       />
        <result property="isDel"             column="is_del"            />
    </resultMap>

    <sql id="selectAlertVo">
        select id, title, description, severity, status, alert_type, source_ip, target_ip, url,
               event_id, source_type, source_sub_type, source_ident, source_name, rule_name, group_name,
               occurred_at, detected_at, resolved_at, resolved_by, resolved_by_name, resolution_note,
               duration_ms, tags, attributes, raw_data,
               create_at, create_by, create_name, update_at, update_by, update_name, is_del
        from alert
    </sql>

    <select id="selectAlertList" parameterType="com.wiwj.securio.alert.domain.Alert" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        <where>
            is_del = 0
            <if test="id != null "> and id = #{id}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="severity != null  and severity != ''"> and severity = #{severity}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="alertType != null  and alertType != ''"> and alert_type = #{alertType}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="sourceSubType != null  and sourceSubType != ''"> and source_sub_type = #{sourceSubType}</if>
            <if test="sourceIdent != null  and sourceIdent != ''"> and source_ident = #{sourceIdent}</if>
            <if test="sourceName != null  and sourceName != ''"> and source_name like concat('%', #{sourceName}, '%')</if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name = #{ruleName}</if>
            <if test="groupName != null  and groupName != ''"> and group_name = #{groupName}</if>
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="occurredAt != null "> and occurred_at = #{occurredAt}</if>
            <if test="detectedAt != null "> and detected_at = #{detectedAt}</if>
            <if test="resolvedAt != null "> and resolved_at = #{resolvedAt}</if>
            <if test="resolvedBy != null  and resolvedBy != ''"> and resolved_by = #{resolvedBy}</if>
            <if test="resolvedByName != null  and resolvedByName != ''"> and resolved_by_name like concat('%', #{resolvedByName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectAlertById" parameterType="Long" resultMap="AlertResult">
        <include refid="selectAlertVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertAlert" parameterType="com.wiwj.securio.alert.domain.Alert" useGeneratedKeys="true" keyProperty="id">
        insert into alert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="severity != null">severity,</if>
            <if test="status != null">status,</if>
            <if test="alertType != null">alert_type,</if>
            <if test="sourceIp != null">source_ip,</if>
            <if test="targetIp != null">target_ip,</if>
            <if test="url != null">url,</if>
            <if test="eventId != null">event_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="sourceSubType != null">source_sub_type,</if>
            <if test="sourceIdent != null">source_ident,</if>
            <if test="sourceName != null">source_name,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="groupName != null">group_name,</if>
            <if test="occurredAt != null">occurred_at,</if>
            <if test="detectedAt != null">detected_at,</if>
            <if test="resolvedAt != null">resolved_at,</if>
            <if test="resolvedBy != null">resolved_by,</if>
            <if test="resolvedByName != null">resolved_by_name,</if>
            <if test="resolutionNote != null">resolution_note,</if>
            <if test="durationMs != null">duration_ms,</if>
            <if test="tags != null">tags,</if>
            <if test="attributes != null">attributes,</if>
            <if test="rawData != null">raw_data,</if>
            <if test="createAt != null">create_at,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createName != null">create_name,</if>
            <if test="updateAt != null">update_at,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateName != null">update_name,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createAt == null">create_at,</if>
            <if test="updateAt == null">update_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="severity != null">#{severity},</if>
            <if test="status != null">#{status},</if>
            <if test="alertType != null">#{alertType},</if>
            <if test="sourceIp != null">#{sourceIp},</if>
            <if test="targetIp != null">#{targetIp},</if>
            <if test="url != null">#{url},</if>
            <if test="eventId != null">#{eventId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="sourceSubType != null">#{sourceSubType},</if>
            <if test="sourceIdent != null">#{sourceIdent},</if>
            <if test="sourceName != null">#{sourceName},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="occurredAt != null">#{occurredAt},</if>
            <if test="detectedAt != null">#{detectedAt},</if>
            <if test="resolvedAt != null">#{resolvedAt},</if>
            <if test="resolvedBy != null">#{resolvedBy},</if>
            <if test="resolvedByName != null">#{resolvedByName},</if>
            <if test="resolutionNote != null">#{resolutionNote},</if>
            <if test="durationMs != null">#{durationMs},</if>
            <if test="tags != null">#{tags},</if>
            <if test="attributes != null">#{attributes},</if>
            <if test="rawData != null">#{rawData},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createName != null">#{createName},</if>
            <if test="updateAt != null">#{updateAt},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createAt == null">#{createAt},</if>
            <if test="updateAt == null">#{updateAt},</if>
         </trim>
    </insert>

    <update id="updateAlert" parameterType="com.wiwj.securio.alert.domain.Alert">
        update alert
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="status != null">status = #{status},</if>
            <if test="alertType != null">alert_type = #{alertType},</if>
            <if test="sourceIp != null">source_ip = #{sourceIp},</if>
            <if test="targetIp != null">target_ip = #{targetIp},</if>
            <if test="url != null">url = #{url},</if>
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="sourceSubType != null">source_sub_type = #{sourceSubType},</if>
            <if test="sourceIdent != null">source_ident = #{sourceIdent},</if>
            <if test="sourceName != null">source_name = #{sourceName},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="occurredAt != null">occurred_at = #{occurredAt},</if>
            <if test="detectedAt != null">detected_at = #{detectedAt},</if>
            <if test="resolvedAt != null">resolved_at = #{resolvedAt},</if>
            <if test="resolvedBy != null">resolved_by = #{resolvedBy},</if>
            <if test="resolvedByName != null">resolved_by_name = #{resolvedByName},</if>
            <if test="resolutionNote != null">resolution_note = #{resolutionNote},</if>
            <if test="durationMs != null">duration_ms = #{durationMs},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="attributes != null">attributes = #{attributes},</if>
            <if test="rawData != null">raw_data = #{rawData},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateAt == null">update_at = #{updateAt},</if>
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <delete id="deleteAlertById" parameterType="Long">
        delete from alert where id = #{id}
    </delete>

    <delete id="deleteAlertByIds" parameterType="String">
        delete from alert where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDeleteAlertById" parameterType="Long">
        update alert set is_del = 1, update_at = now() where id = #{id} and is_del = 0
    </update>

    <update id="logicDeleteAlertByIds">
        update alert set is_del = 1, update_at = now() where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>

    <!-- 获取所有非空的标签数据 -->
    <select id="selectAllTags" resultType="String">
        select distinct tags
        from alert
        where tags is not null
          and tags != ''
          and tags != '{}'
          and is_del = 0
    </select>

    <!-- ==================== 统计相关SQL ==================== -->

    <!-- 查询告警总数 -->
    <select id="selectTotalCount" resultType="int">
        select count(1)
        from alert
        where is_del = 0
    </select>

    <!-- 根据严重程度查询告警数量 -->
    <select id="selectCountBySeverity" parameterType="String" resultType="int">
        select count(1)
        from alert
        where severity = #{severity}
          and is_del = 0
    </select>

    <!-- 根据状态查询告警数量 -->
    <select id="selectCountByStatus" parameterType="String" resultType="int">
        select count(1)
        from alert
        where status = #{status}
          and is_del = 0
    </select>

    <!-- 根据状态列表查询告警数量 -->
    <select id="selectCountByStatusIn" resultType="int">
        select count(1)
        from alert
        where status in
        <foreach item="status" collection="array" open="(" separator="," close=")">
            #{status}
        </foreach>
        and is_del = 0
    </select>

    <!-- 查询今日新增告警数量 -->
    <select id="selectTodayCount" resultType="int">
        select count(1)
        from alert
        where date(from_unixtime(create_at/1000)) = curdate()
          and is_del = 0
    </select>

    <!-- 查询平均处理时间（小时） -->
    <select id="selectAvgResolutionTime" resultType="Double">
        select avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and create_at is not null
          and resolved_at > create_at
          and is_del = 0
    </select>

    <!-- 查询告警趋势数据 -->
    <select id="selectAlertTrendByDays" parameterType="Integer" resultType="Map">
        select
            date(from_unixtime(create_at/1000)) as date,
            count(1) as count,
            sum(case when severity = 'critical' then 1 else 0 end) as critical_count,
            sum(case when severity = 'high' then 1 else 0 end) as high_count,
            sum(case when severity = 'medium' then 1 else 0 end) as medium_count,
            sum(case when severity = 'low' then 1 else 0 end) as low_count
        from alert
        where create_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
        group by date(from_unixtime(create_at/1000))
        order by date desc
    </select>

    <!-- 根据严重程度查询告警分布 -->
    <select id="selectAlertDistributionBySeverity" resultType="Map">
        select
            severity as name,
            count(1) as value
        from alert
        where is_del = 0
        group by severity
        order by value desc
    </select>

    <!-- 根据状态查询告警分布 -->
    <select id="selectAlertDistributionByStatus" resultType="Map">
        select
            status as name,
            count(1) as value
        from alert
        where is_del = 0
        group by status
        order by value desc
    </select>

    <!-- 根据告警源类型查询告警分布 -->
    <select id="selectAlertDistributionBySourceType" resultType="Map">
        select
            source_type as name,
            count(1) as value
        from alert
        where is_del = 0
          and source_type is not null
        group by source_type
        order by value desc
    </select>

    <!-- 查询热点告警源 -->
    <select id="selectTopAlertSources" parameterType="Integer" resultType="Map">
        select
            coalesce(source_name, source_ident, source_type, '未知') as source_name,
            source_type,
            count(1) as alert_count,
            sum(case when severity = 'critical' then 1 else 0 end) as critical_count,
            max(create_at) as last_alert_time
        from alert
        where is_del = 0
          and create_at >= unix_timestamp(date_sub(curdate(), interval 7 day)) * 1000
        group by coalesce(source_name, source_ident, source_type), source_type
        order by alert_count desc
        limit #{limit}
    </select>

    <!-- 根据天数查询已解决告警数量 -->
    <select id="selectResolvedCountByDays" parameterType="Integer" resultType="int">
        select count(1)
        from alert
        where resolved_at is not null
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
    </select>

    <!-- 根据天数查询平均处理时间 -->
    <select id="selectAvgResolutionTimeByDays" parameterType="Integer" resultType="Double">
        select avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and create_at is not null
          and resolved_at > create_at
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
    </select>

    <!-- 根据天数和处理人查询解决统计 -->
    <select id="selectResolutionStatsByResolver" parameterType="Integer" resultType="Map">
        select
            resolved_by_name as resolver_name,
            count(1) as resolved_count,
            avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and resolved_by_name is not null
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
        group by resolved_by_name
        order by resolved_count desc
    </select>

    <!-- 根据天数查询每日处理统计 -->
    <select id="selectDailyResolutionStats" parameterType="Integer" resultType="Map">
        select
            date(from_unixtime(resolved_at/1000)) as date,
            count(1) as resolved_count,
            avg((resolved_at - create_at) / (1000 * 60 * 60)) as avg_resolution_time
        from alert
        where resolved_at is not null
          and create_at is not null
          and resolved_at >= unix_timestamp(date_sub(curdate(), interval #{days} day)) * 1000
          and is_del = 0
        group by date(from_unixtime(resolved_at/1000))
        order by date desc
    </select>

</mapper>
