<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wiwj.securio.intelmgr.mapper.PatchSupersededMapper">
    
    <resultMap type="PatchSuperseded" id="PatchSupersededResult">
        <result property="id"    column="id"    />
        <result property="patchId"    column="patch_id"    />
        <result property="supersededId"    column="superseded_id"    />
        <result property="title"    column="title"    />
        <result property="titleEn"    column="title_en"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectPatchSupersededVo">
        select id, patch_id, superseded_id, title, title_en, created_at from patch_superseded
    </sql>

    <select id="selectPatchSupersededList" parameterType="PatchSuperseded" resultMap="PatchSupersededResult">
        <include refid="selectPatchSupersededVo"/>
        <where>  
            <if test="patchId != null  and patchId != ''"> and patch_id = #{patchId}</if>
            <if test="supersededId != null  and supersededId != ''"> and superseded_id = #{supersededId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="titleEn != null  and titleEn != ''"> and title_en = #{titleEn}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectPatchSupersededById" parameterType="Integer" resultMap="PatchSupersededResult">
        <include refid="selectPatchSupersededVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPatchSuperseded" parameterType="PatchSuperseded" useGeneratedKeys="true" keyProperty="id">
        insert into patch_superseded
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id,</if>
            <if test="supersededId != null and supersededId != ''">superseded_id,</if>
            <if test="title != null">title,</if>
            <if test="titleEn != null">title_en,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">#{patchId},</if>
            <if test="supersededId != null and supersededId != ''">#{supersededId},</if>
            <if test="title != null">#{title},</if>
            <if test="titleEn != null">#{titleEn},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updatePatchSuperseded" parameterType="PatchSuperseded">
        update patch_superseded
        <trim prefix="SET" suffixOverrides=",">
            <if test="patchId != null and patchId != ''">patch_id = #{patchId},</if>
            <if test="supersededId != null and supersededId != ''">superseded_id = #{supersededId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="titleEn != null">title_en = #{titleEn},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatchSupersededById" parameterType="Integer">
        delete from patch_superseded where id = #{id}
    </delete>

    <delete id="deletePatchSupersededByIds" parameterType="String">
        delete from patch_superseded where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>