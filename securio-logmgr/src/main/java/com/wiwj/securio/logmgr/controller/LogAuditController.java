package com.wiwj.securio.logmgr.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 审计日志控制器
 *
 * <AUTHOR>
 * @Date 2025/02/28
 * @Version 1.0
 */
@Api(tags = "审计日志管理", description = "审计日志相关接口")
@RestController
@RequestMapping("/opslog/wukong")
public class LogAuditController extends BaseController {

    private static final Logger auditLogger = LoggerFactory.getLogger("auditLogger");

    /**
     * 接收审计日志
     *
     * @param auditLog 审计日志数据
     * @return 处理结果
     */
    @ApiOperation(value = "接收审计日志", notes = "接收并存储审计日志数据")
    @PostMapping("/auditlog")
    public AjaxResult auditlog(@ApiParam(value = "审计日志数据", required = true) String auditLog) {

        // 打印审计日志到控制台和专门的日志文件
        auditLogger.info(" {}",auditLog);

        // 返回成功结果
        return AjaxResult.success();
    }
}
