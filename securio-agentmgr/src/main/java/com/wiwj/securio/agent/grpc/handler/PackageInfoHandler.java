package com.wiwj.securio.agent.grpc.handler;

import com.wiwj.securio.agent.domain.AgentInfo;
import com.wiwj.securio.agent.domain.InventoryPackageInfo;
import com.wiwj.securio.agent.grpc.proto.PackageInfoData;
import com.wiwj.securio.agent.grpc.proto.PackageInfoRequest;
import com.wiwj.securio.agent.grpc.proto.PackageInfoResponse;
import com.wiwj.securio.agent.service.IAgentInfoService;
import com.wiwj.securio.agent.service.IInventoryPackageInfoService;
import io.grpc.stub.StreamObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 软件包信息上报处理器
 */
@Component
public class PackageInfoHandler extends AbstractGrpcRequestHandler<PackageInfoRequest, PackageInfoResponse> {

    @Autowired
    private IAgentInfoService agentInfoService;

    @Autowired
    private IInventoryPackageInfoService packageInfoService;

    @Override
    public void handle(PackageInfoRequest request, StreamObserver<PackageInfoResponse> responseObserver) {
        logger.info("Received package info from agent: {}, page: {}/{}, isInitial: {}",
                request.getUuid(), request.getPage(), request.getTotalPages(), request.getIsInitial());

        try {
            // 验证token
            if (!validateToken(request.getToken())) {
                PackageInfoResponse response = PackageInfoResponse.newBuilder()
                        .setSuccess(false)
                        .setError("Invalid token")
                        .build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }

            // 如果是第一页，清理旧数据
            if (request.getPage() == 1) {
                // 删除该 agent 的所有软件包信息
                int deleteCount = packageInfoService.deleteInventoryPackageInfoByAgentId(request.getUuid());
                logger.info("Deleted {} package info records for agent: {}", deleteCount, request.getUuid());
            }
            AgentInfo agentInfo = agentInfoService.selectAgentInfoByAgentId(request.getUuid());
            if (agentInfo == null) {
                PackageInfoResponse response = PackageInfoResponse.newBuilder()
                        .setSuccess(false)
                        .setError("Agent not found")
                        .build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }

            // 保存软件包信息到数据库
            List<InventoryPackageInfo> packages = new ArrayList<>();

            for (PackageInfoData packageData : request.getPackagesList()) {
                InventoryPackageInfo packageInfo = new InventoryPackageInfo();
                packageInfo.setAgentId(request.getUuid());
                packageInfo.setName(packageData.getName());
                packageInfo.setVersion(packageData.getVersion());
                packageInfo.setInstallTime(packageData.getInstallTime());
                packageInfo.setLocation(packageData.getLocation());
                packageInfo.setArchitecture(packageData.getArchitecture());
                packageInfo.setVendor(packageData.getVendor());
                packageInfo.setDescription(packageData.getDescription());
                packageInfo.setSize(packageData.getSize());
                packageInfo.setFormat(packageData.getFormat());
                packageInfo.setCreateBy("system");
                packageInfo.setUpdateBy("system");
                packageInfo.setHostIp(agentInfo.getHostIp());
                packageInfo.setIsDel(0);
                packageInfo.setCreateAt(System.currentTimeMillis());
                packageInfo.setUpdateAt(System.currentTimeMillis());
                packages.add(packageInfo);
            }

            // 批量插入软件包信息
            if (!packages.isEmpty()) {
                packageInfoService.batchInsertInventoryPackageInfo(packages);
            }

            logger.info("Saved {} packages for agent: {}, page: {}",
                    packages.size(), request.getUuid(), request.getPage());

            PackageInfoResponse response = PackageInfoResponse.newBuilder()
                    .setSuccess(true)
                    .setError("")
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            logger.error("Error processing package info for agent: " + request.getUuid(), e);
            PackageInfoResponse response = PackageInfoResponse.newBuilder()
                    .setSuccess(false)
                    .setError("Internal error: " + e.getMessage())
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }
}
