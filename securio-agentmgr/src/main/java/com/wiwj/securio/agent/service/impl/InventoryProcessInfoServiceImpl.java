package com.wiwj.securio.agent.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.agent.mapper.InventoryProcessInfoMapper;
import com.wiwj.securio.agent.domain.InventoryProcessInfo;
import com.wiwj.securio.agent.service.IInventoryProcessInfoService;

/**
 * 存储Agent主机的进程信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Service
public class InventoryProcessInfoServiceImpl implements IInventoryProcessInfoService {
    @Autowired
    private InventoryProcessInfoMapper inventoryProcessInfoMapper;

    /**
     * 查询存储Agent主机的进程信息
     *
     * @param id 存储Agent主机的进程信息主键
     * @return 存储Agent主机的进程信息
     */
    @Override
    public InventoryProcessInfo selectInventoryProcessInfoById(Long id) {
        return inventoryProcessInfoMapper.selectInventoryProcessInfoById(id);
    }

    /**
     * 查询存储Agent主机的进程信息列表
     *
     * @param inventoryProcessInfo 存储Agent主机的进程信息
     * @return 存储Agent主机的进程信息
     */
    @Override
    public List<InventoryProcessInfo> selectInventoryProcessInfoList(InventoryProcessInfo inventoryProcessInfo) {
        return inventoryProcessInfoMapper.selectInventoryProcessInfoList(inventoryProcessInfo);
    }

    /**
     * 新增存储Agent主机的进程信息
     *
     * @param inventoryProcessInfo 存储Agent主机的进程信息
     * @return 结果
     */
    @Override
    public int insertInventoryProcessInfo(InventoryProcessInfo inventoryProcessInfo) {
        inventoryProcessInfo.setIsDel(0);
        inventoryProcessInfo.setCreateAt(System.currentTimeMillis());
        inventoryProcessInfo.setUpdateAt(System.currentTimeMillis());
        return inventoryProcessInfoMapper.insertInventoryProcessInfo(inventoryProcessInfo);
    }

    /**
     * 修改存储Agent主机的进程信息
     *
     * @param inventoryProcessInfo 存储Agent主机的进程信息
     * @return 结果
     */
    @Override
    public int updateInventoryProcessInfo(InventoryProcessInfo inventoryProcessInfo) {
        return inventoryProcessInfoMapper.updateInventoryProcessInfo(inventoryProcessInfo);
    }

    /**
     * 批量删除存储Agent主机的进程信息
     *
     * @param ids 需要删除的存储Agent主机的进程信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryProcessInfoByIds(Long[] ids) {
        return inventoryProcessInfoMapper.deleteInventoryProcessInfoByIds(ids);
    }

    /**
     * 删除存储Agent主机的进程信息信息
     *
     * @param id 存储Agent主机的进程信息主键
     * @return 结果
     */
    @Override
    public int deleteInventoryProcessInfoById(Long id) {
        return inventoryProcessInfoMapper.deleteInventoryProcessInfoById(id);
    }

    /**
     * 批量新增存储Agent主机的进程信息
     *
     * @param inventoryProcessInfoList 存储Agent主机的进程信息列表
     * @return 结果
     */
    @Override
    public int batchInsertInventoryProcessInfo(List<InventoryProcessInfo> inventoryProcessInfoList) {
        return inventoryProcessInfoMapper.batchInsertInventoryProcessInfo(inventoryProcessInfoList);
    }

    /**
     * 根据Agent ID删除存储Agent主机的进程信息
     *
     * @param agentId Agent ID
     * @return 结果
     */
    @Override
    public int deleteInventoryProcessInfoByAgentId(String agentId) {
        return inventoryProcessInfoMapper.deleteInventoryProcessInfoByAgentId(agentId);
    }
}
