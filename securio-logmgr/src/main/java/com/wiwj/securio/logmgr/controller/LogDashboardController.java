package com.wiwj.securio.logmgr.controller;

import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.securio.logmgr.domain.dto.LogStatsDTO;
import com.wiwj.securio.logmgr.service.LogStatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 日志仪表盘控制器
 *
 * <AUTHOR>
 */
@Api(tags = "日志仪表盘管理", description = "日志仪表盘相关接口")
@RestController
@RequestMapping("/opslog/dashboard")
public class LogDashboardController extends BaseController {

    @Autowired
    private LogStatsService logStatsService;

    /**
     * 获取日志统计数据
     *
     * @return 日志统计数据
     */
    @ApiOperation(value = "获取日志统计数据", notes = "获取系统日志的统计信息")
    @GetMapping("/stats")
    public AjaxResult getLogStats() {
        List<LogStatsDTO> stats = logStatsService.getLogStats();
        return AjaxResult.success(stats);
    }
}
