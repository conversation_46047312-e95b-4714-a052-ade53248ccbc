<template>
  <el-drawer
      title="配置表格列"
      :visible.sync="visible"
      direction="rtl"
      size="40%"
  >
    <el-transfer
        class="custom-transfer"
        v-model="selectedColumns"
        :data="allColumns"
        :titles="['可选列', '显示列']"
        :props="{ key: 'name', label: 'alias' }"
    />
    <div class="drawer-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="saveConfig">保存</el-button>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    // 所有列
    allColumns: {
      type: Array,
      required: true,
    },
    // 当前选中的列
    value: {
      type: Array,
      required: true,
    },
    // 缓存 key
    cacheKey: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      visible: false, // 控制抽屉显示
      selectedColumns: this.value, // 当前选中的列
    };
  },
  watch: {
    // 监听 value 的变化，更新 selectedColumns
    value(newVal) {
      this.selectedColumns = newVal;
    },
  },
  methods: {
    // 打开抽屉
    open() {
      this.visible = true;
    },
    // 保存配置
    saveConfig() {
      this.$emit('input', this.selectedColumns); // 更新父组件的选中列
      localStorage.setItem(this.cacheKey, JSON.stringify(this.selectedColumns)); // 缓存配置
      this.visible = false;
    },
  },
};
</script>

<style scoped>
.drawer-footer {
  position: absolute;
  bottom: 20px;
  right: 20px;
}
.custom-transfer {
  margin-left: 20px; /* 卡片距离抽屉左侧 40px */
  /*margin-top: 10px;  !* 卡片距离抽屉顶部 20px *!*/
  margin-right: 20px; /* 卡片距离抽屉右侧 20px */
  margin-bottom: 20px; /* 卡片距离抽屉底部 20px */
}
</style>
