package com.wiwj.securio.alert.domain;

import com.wiwj.common.annotation.Excel;
import com.wiwj.securio.alert.enums.AlertStatusEnum;
import lombok.Data;

/**
 * 告警对象 alert
 *
 * <AUTHOR>
 */
@Data
public class Alert {
    private static final long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    private Long id;

    /**
     * 告警标题
     */
    @Excel(name = "告警标题")
    private String title;

    /**
     * 告警详细描述
     */
    @Excel(name = "告警详细描述")
    private String description;

    /**
     * 严重程度(critical/high/medium/low/info)
     */
    @Excel(name = "严重程度", readConverterExp = "critical=严重,high=高危,medium=中危,low=低危,info=信息")
    private String severity;

    /**
     * 状态(triggered/acknowledged/resolved/suppressed)
     */
    @Excel(name = "状态", readConverterExp = "triggered=触发,acknowledged=确认,resolved=已解决,suppressed=沉默")
    private String status;

    /**
     * 告警类型
     */
    @Excel(name = "告警类型")
    private String alertType;


    /**
     * 源IP地址
     */
    @Excel(name = "源IP地址,比如攻击源IP地址")
    private String sourceIp;

    /**
     * 目标IP地址
     */
    @Excel(name = "目标IP地址,比如告警主机IP地址")
    private String targetIp;

    /**
     * URL路径，告警详情url
     */
    private String url;

    /**
     * 事件ID，源系统中的唯一标识
     */
    @Excel(name = "事件ID")
    private String eventId;

    /**
     * 告警规则名称
     */
    @Excel(name = "告警规则名称")
    private String ruleName;

    /**
     * 告警组名称
     */
    @Excel(name = "告警组名称")
    private String groupName;

    /**
     * 告警类型 牧云/网宿/n9e等
     */
    @Excel(name = "告警类型")
    private String sourceType;

    /**
     * 来源子类型 如zeek-notice/zeek-eve/zeek-weird等
     */
    @Excel(name = "来源子类型")
    private String sourceSubType;

    /**
     * 告警源实例唯一标识
     */
    @Excel(name = "告警源实例唯一标识")
    private String sourceIdent;

    /**
     * 告警源实例名称
     */
    @Excel(name = "告警源实例名称")
    private String sourceName;

    /**
     * 告警发生时间（时间戳，毫秒）
     */
    @Excel(name = "告警发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long occurredAt;

    /**
     * 告警检测时间（时间戳，毫秒）
     */
    @Excel(name = "告警检测时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long detectedAt;

    /**
     * 告警解决时间（时间戳，毫秒）
     */
    @Excel(name = "告警解决时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long resolvedAt;

    /**
     * 解决人ID
     */
    private String resolvedBy;

    /**
     * 解决人名称
     */
    @Excel(name = "解决人")
    private String resolvedByName;

    /**
     * 解决说明
     */
    @Excel(name = "解决说明")
    private String resolutionNote;

    /**
     * 告警持续时间（毫秒）
     */
    @Excel(name = "持续时间")
    private Long durationMs;

    /**
     * 告警标签，JSON格式存储键值对，用于分类和筛选
     */
    private String tags;

    /**
     * 告警属性，JSON格式存储复杂对象，包含上下文信息和扩展数据
     */
    private String attributes;

    /**
     * 原始数据
     */
    private String rawData;

    /**
     * 创建时间
     */
    private Long createAt;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 更新时间
     */
    private Long updateAt;

    /**
     * 更新人ID
     */
    private String updateBy;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 是否删除(0-未删除 1-已删除)
     */
    private Integer isDel;

    // ==================== 便捷方法 ====================

    /**
     * 获取告警持续时间（毫秒）
     * 如果告警已解决，返回 resolved_at - occurred_at
     * 如果告警未解决，返回 当前时间 - occurred_at
     */
    public Long getDurationMs() {
        if (this.durationMs != null) {
            return this.durationMs;
        }

        if (this.occurredAt == null) {
            return null;
        }

        if (this.resolvedAt != null) {
            return this.resolvedAt - this.occurredAt;
        } else {
            return System.currentTimeMillis() - this.occurredAt;
        }
    }

    /**
     * 设置持续时间（自动计算）
     */
    public void calculateDuration() {
        if (this.occurredAt != null && this.resolvedAt != null) {
            this.durationMs = this.resolvedAt - this.occurredAt;
        }
    }

    /**
     * 获取格式化的持续时间字符串
     */
    public String getFormattedDuration() {
        Long duration = getDurationMs();
        if (duration == null) {
            return "-";
        }

        long seconds = duration / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return String.format("%d天%d小时", days, hours % 24);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 检查告警是否已解决
     */
    public boolean isResolved() {
        return AlertStatusEnum.RESOLVED.getCode().equals(this.status) || AlertStatusEnum.SUPPRESSED.getCode().equals(this.status);
    }

    /**
     * 检查告警是否活跃
     */
    public boolean isActive() {
        return AlertStatusEnum.TRIGGERED.getCode().equals(this.status) || AlertStatusEnum.ACKNOWLEDGED.getCode().equals(this.status);
    }
}
