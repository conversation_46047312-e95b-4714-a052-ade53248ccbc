package com.wiwj.securio.intelmgr.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wiwj.common.annotation.Log;
import com.wiwj.common.core.controller.BaseController;
import com.wiwj.common.core.domain.AjaxResult;
import com.wiwj.common.enums.BusinessType;
import com.wiwj.securio.intelmgr.domain.Patch;
import com.wiwj.securio.intelmgr.domain.PatchTrait;
import com.wiwj.securio.intelmgr.domain.PatchAffectProduct;
import com.wiwj.securio.intelmgr.domain.PatchAffectPackage;
import com.wiwj.securio.intelmgr.domain.PatchCve;
import com.wiwj.securio.intelmgr.domain.PatchReference;
import com.wiwj.securio.intelmgr.domain.PatchSuperseded;
import com.wiwj.securio.intelmgr.domain.PatchSuperseding;
import com.wiwj.securio.intelmgr.service.IPatchService;
import com.wiwj.securio.intelmgr.service.IPatchTraitService;
import com.wiwj.securio.intelmgr.service.IPatchAffectProductService;
import com.wiwj.securio.intelmgr.service.IPatchAffectPackageService;
import com.wiwj.securio.intelmgr.service.IPatchCveService;
import com.wiwj.securio.intelmgr.service.IPatchReferenceService;
import com.wiwj.securio.intelmgr.service.IPatchSupersededService;
import com.wiwj.securio.intelmgr.service.IPatchSupersedingService;
import com.wiwj.common.utils.poi.ExcelUtil;
import com.wiwj.common.core.page.TableDataInfo;
import java.util.stream.Collectors;

/**
 * 补丁基本信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/intelmgr/patch")
public class PatchController extends BaseController {
    @Autowired
    private IPatchService patchService;

    @Autowired
    private IPatchTraitService patchTraitService;
    
    @Autowired
    private IPatchAffectProductService patchAffectProductService;
    
    @Autowired
    private IPatchAffectPackageService patchAffectPackageService;
    
    @Autowired
    private IPatchCveService patchCveService;
    
    @Autowired
    private IPatchReferenceService patchReferenceService;
    
    @Autowired
    private IPatchSupersededService patchSupersededService;
    
    @Autowired
    private IPatchSupersedingService patchSupersedingService;

    /**
     * 查询补丁基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:list')")
    @GetMapping("/list")
    public TableDataInfo list(Patch patch) {
        startPage();
        List<Patch> list = patchService.selectPatchList(patch);
        
        // 获取补丁特性信息
        if (list != null && !list.isEmpty()) {
            // 获取所有补丁ID
            List<String> patchIds = list.stream().map(Patch::getId).collect(Collectors.toList());
            
            // 获取补丁特性信息
            List<PatchTrait> allTraits = patchTraitService.selectPatchTraitByPatchIds(patchIds);
            
            // 将补丁特性信息按补丁ID分组
            Map<String, List<PatchTrait>> traitMap = allTraits.stream()
                    .collect(Collectors.groupingBy(PatchTrait::getPatchId));
            
            // 设置补丁特性
            for (Patch p : list) {
                List<PatchTrait> traits = traitMap.get(p.getId());
                if (traits != null && !traits.isEmpty()) {
                    // 将补丁特性信息添加到feature字段，多个特性用逗号分隔
                    String feature = traits.stream()
                            .map(PatchTrait::getTrait)
                            .collect(Collectors.joining(","));
                    p.setFeature(feature);
                }
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 导出补丁基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:export')")
    @Log(title = "补丁基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Patch patch) {
        List<Patch> list = patchService.selectPatchList(patch);
        ExcelUtil<Patch> util = new ExcelUtil<Patch>(Patch.class);
        util.exportExcel(response, list, "补丁基本信息数据");
    }

    /**
     * 获取补丁基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(patchService.selectPatchById(id));
    }

    /**
     * 新增补丁基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:add')")
    @Log(title = "补丁基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Patch patch) {
        return toAjax(patchService.insertPatch(patch));
    }

    /**
     * 修改补丁基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:edit')")
    @Log(title = "补丁基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Patch patch) {
        return toAjax(patchService.updatePatch(patch));
    }

    /**
     * 删除补丁基本信息
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:remove')")
    @Log(title = "补丁基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(patchService.deletePatchByIds(ids));
    }

    /**
     * 获取补丁详细信息（包含关联数据）
     */
    @PreAuthorize("@ss.hasPermi('intelmgr:patch:query')")
    @GetMapping(value = "/detail/{id}")
    public AjaxResult getDetail(@PathVariable("id") String id) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取补丁基本信息
        Patch patch = patchService.selectPatchById(id);
        if (patch == null) {
            return AjaxResult.error("补丁信息不存在");
        }
        result.put("basic", patch);
        
        // 获取补丁特性信息
        PatchTrait traitQuery = new PatchTrait();
        traitQuery.setPatchId(id);
        List<PatchTrait> traits = patchTraitService.selectPatchTraitList(traitQuery);
        result.put("traits", traits);
        
        // 获取补丁影响产品信息
        PatchAffectProduct productQuery = new PatchAffectProduct();
        productQuery.setPatchId(id);
        List<PatchAffectProduct> products = patchAffectProductService.selectPatchAffectProductList(productQuery);
        result.put("affectProducts", products);
        
        // 获取补丁影响包信息
        PatchAffectPackage packageQuery = new PatchAffectPackage();
        packageQuery.setPatchId(id);
        List<PatchAffectPackage> packages = patchAffectPackageService.selectPatchAffectPackageList(packageQuery);
        result.put("affectPackages", packages);
        
        // 获取补丁CVE信息
        PatchCve cveQuery = new PatchCve();
        cveQuery.setPatchId(id);
        List<PatchCve> cves = patchCveService.selectPatchCveList(cveQuery);
        result.put("cves", cves);
        
        // 获取补丁参考链接信息
        PatchReference referenceQuery = new PatchReference();
        referenceQuery.setPatchId(id);
        List<PatchReference> references = patchReferenceService.selectPatchReferenceList(referenceQuery);
        result.put("references", references);
        
        // 获取被替代的补丁信息
        PatchSuperseded supersededQuery = new PatchSuperseded();
        supersededQuery.setPatchId(id);
        List<PatchSuperseded> superseded = patchSupersededService.selectPatchSupersededList(supersededQuery);
        result.put("superseded", superseded);
        
        // 获取替代补丁信息
        PatchSuperseding supersedingQuery = new PatchSuperseding();
        supersedingQuery.setPatchId(id);
        List<PatchSuperseding> superseding = patchSupersedingService.selectPatchSupersedingList(supersedingQuery);
        result.put("superseding", superseding);
        
        return success(result);
    }
}
