package com.wiwj.securio.agent.mapper;

import java.util.List;
import com.wiwj.securio.agent.domain.InventoryStartupInfo;

/**
 * 存储系统启动项信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface InventoryStartupInfoMapper {
    /**
     * 查询存储系统启动项信息
     *
     * @param id 存储系统启动项信息主键
     * @return 存储系统启动项信息
     */
    public InventoryStartupInfo selectInventoryStartupInfoById(Long id);

    /**
     * 查询存储系统启动项信息列表
     *
     * @param inventoryStartupInfo 存储系统启动项信息
     * @return 存储系统启动项信息集合
     */
    public List<InventoryStartupInfo> selectInventoryStartupInfoList(InventoryStartupInfo inventoryStartupInfo);

    /**
     * 新增存储系统启动项信息
     *
     * @param inventoryStartupInfo 存储系统启动项信息
     * @return 结果
     */
    public int insertInventoryStartupInfo(InventoryStartupInfo inventoryStartupInfo);

    /**
     * 修改存储系统启动项信息
     *
     * @param inventoryStartupInfo 存储系统启动项信息
     * @return 结果
     */
    public int updateInventoryStartupInfo(InventoryStartupInfo inventoryStartupInfo);

    /**
     * 删除存储系统启动项信息
     *
     * @param id 存储系统启动项信息主键
     * @return 结果
     */
    public int deleteInventoryStartupInfoById(Long id);

    /**
     * 批量删除存储系统启动项信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryStartupInfoByIds(Long[] ids);

    /**
     * 批量新增存储系统启动项信息
     *
     * @param inventoryStartupInfoList 存储系统启动项信息列表
     * @return 结果
     */
    public int batchInsertInventoryStartupInfo(List<InventoryStartupInfo> inventoryStartupInfoList);

    /**
     * 根据Agent ID删除存储Agent主机的进程信息
     *
     * @param agentId Agent ID
     * @return 结果
     */
    public int deleteInventoryStartupInfoByAgentId(String agentId);
}
