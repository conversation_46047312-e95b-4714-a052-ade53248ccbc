<template>
    <div class="audit-overview oracle-stat-overview" v-loading="loading" element-loading-text="加载中...">
      <!-- 全局控制面板 -->
      <div class="global-controls-panel">
        <div class="global-title">时间范围控制</div>
        <div class="global-controls">
          <el-select v-model="globalTimeRange" placeholder="选择时间范围" size="small" @change="handleGlobalTimeRangeChange">
            <el-option label="最近 24 小时" value="1d"></el-option>
            <el-option label="最近 3 天" value="3d"></el-option>
            <el-option label="最近 7 天" value="7d"></el-option>
            <el-option label="最近 14 天" value="14d"></el-option>
            <el-option label="最近 30 天" value="30d"></el-option>
          </el-select>
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            inactive-text=""
            @change="handleAutoRefreshChange">
          </el-switch>
          <el-button type="primary" icon="el-icon-refresh" size="small" @click="refreshAllData">刷新数据</el-button>
        </div>
      </div>

      <!-- 统计数据卡片 -->
      <h3 class="section-title">数据总览</h3>
      <div class="stat-cards-container">
        <div class="stat-card" v-for="(item, index) in statItems" :key="index">
          <div class="stat-circle" :style="{ backgroundColor: item.bgColor }">
            <span class="stat-number">{{ item.value }}</span>
          </div>
          <div class="stat-info">
            <div class="stat-title">{{ item.title }}</div>
            <div class="stat-details">
              <div class="stat-detail">
                <span class="stat-label">今日新增</span>
                <span class="stat-value" :style="{ color: item.color }">{{ item.today }}</span>
              </div>
              <div class="stat-detail">
                <span class="stat-label">昨日新增</span>
                <span class="stat-value" :style="{ color: item.color }">{{ item.yesterday }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据流向统计（桑基图） -->
      <h3 class="section-title">数据流向统计</h3>
      <div class="sankey-controls">
        <el-form :inline="true" size="small">
          <el-form-item label="时间范围">
            <el-select v-model="sankeyForm.timeRange" placeholder="选择时间范围" @change="handleSankeyTimeRangeChange">
              <el-option label="最近 24 小时" value="1d"></el-option>
              <el-option label="最近 3 天" value="3d"></el-option>
              <el-option label="最近 7 天" value="7d"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库数量">
            <el-input-number v-model="sankeyForm.dbLimit" :min="1" :max="20" size="small" @change="fetchSankeyData"></el-input-number>
          </el-form-item>
          <el-form-item label="客户端数量">
            <el-input-number v-model="sankeyForm.clientLimit" :min="1" :max="20" size="small" @change="fetchSankeyData"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <div class="stats-card">
        <div ref="sankeyChart" class="sankey-chart" v-loading="sankeyLoading"></div>
      </div>

      <!-- 热门统计 -->
      <h3 class="section-title">热门统计</h3>
      <div class="stats-row">
        <div class="stats-card" style="flex: 0.4;">
          <h3 class="stats-title">操作类型</h3>
          <div ref="actionTypeChart" class="action-type-chart"></div>
        </div>

        <div class="stats-card" style="flex: 0.6;">
          <h3 class="stats-title">热门客户端和用户</h3>
          <div ref="clientUserChart" class="client-user-chart"></div>
        </div>
      </div>

      <div class="stats-row" style="margin-top: 20px;">
        <div class="stats-card">
          <h3 class="stats-title">热门数据库</h3>
          <el-table :data="topDatabases.slice(0, 10)" size="small" border stripe>
            <el-table-column prop="fieldValue" label="数据库名" width="180"></el-table-column>
            <el-table-column prop="hits" label="访问次数"></el-table-column>
          </el-table>
        </div>

        <div class="stats-card">
          <h3 class="stats-title">热门数据库服务器</h3>
          <el-table :data="topDbServers.slice(0, 10)" size="small" border stripe>
            <el-table-column prop="fieldValue" label="服务器 IP" width="180"></el-table-column>
            <el-table-column prop="hits" label="访问次数"></el-table-column>
          </el-table>
        </div>

        <div class="stats-card">
          <h3 class="stats-title">热门表所有者</h3>
          <el-table :data="topOwners.slice(0, 10)" size="small" border stripe>
            <el-table-column prop="fieldValue" label="所有者" width="180"></el-table-column>
            <el-table-column prop="hits" label="访问次数"></el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 访问时间序列统计 -->
      <h3 class="section-title">访问时间序列统计</h3>
      <div class="time-series-controls">
        <el-form :inline="true" size="small">
          <el-form-item label="统计维度">
            <el-select v-model="timeSeriesForm.dimension" placeholder="选择统计维度" @change="handleTimeSeriesTimeRangeChange">
              <el-option label="服务器 IP" value="server_ip"></el-option>
              <el-option label="数据库服务名" value="db_name"></el-option>
              <el-option label="客户端主机名" value="client_host"></el-option>
              <el-option label="客户端用户名" value="client_user"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-select v-model="timeSeriesForm.timeRange" placeholder="选择时间范围" @change="handleTimeSeriesTimeRangeChange">
              <el-option label="最近 24 小时" value="1d"></el-option>
              <el-option label="最近 3 天" value="3d"></el-option>
              <el-option label="最近 7 天" value="7d"></el-option>
              <el-option label="最近 14 天" value="14d"></el-option>
              <el-option label="最近 30 天" value="30d"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="stats-card">
        <div ref="timeSeriesChart" class="time-series-chart" v-loading="timeSeriesLoading"></div>
      </div>


    </div>
  </template>

  <script>
  import * as echarts from 'echarts'
  import 'echarts/lib/chart/sankey'
  import { getOracleTimeSeriesStats, getOracleDataFlowStats } from '@/api/opslog/oracle'
  import { getFacets } from '@/api/opslog/vmlogs'

  export default {
    name: 'OracleStatOverview',
    data() {
      return {
        loading: false,
        // 统计数据卡片
        statItems: [
          {
            title: '接入数据库数',
            value: '0',
            today: '0',
            yesterday: '0',
            color: '#409EFF',
            bgColor: 'rgba(64, 158, 255, 0.1)'
          },
          {
            title: '登录操作',
            value: '0',
            today: '0',
            yesterday: '0',
            color: '#67C23A',
            bgColor: 'rgba(103, 194, 58, 0.1)'
          },
          {
            title: '数据操作',
            value: '0',
            today: '0',
            yesterday: '0',
            color: '#E6A23C',
            bgColor: 'rgba(230, 162, 60, 0.1)'
          }
        ],
        // 字段统计信息
        fieldStats: {}, // 字段统计信息
        topActionTypes: [], // 热门操作类型
        topUsers: [], // 热门用户
        topHosts: [], // 热门客户端
        topDatabases: [], // 热门数据库
        topDbServers: [], // 热门数据库服务器
        topOwners: [], // 热门表所有者

        // 图表对象
        actionTypeChart: null, // 操作类型玫瑰图
        clientUserChart: null, // 客户端和用户柱状图


        // 时间序列图表
        timeSeriesChart: null,
        timeSeriesLoading: false,
        timeSeriesData: [],
        timeSeriesForm: {
          dimension: 'server_ip', // 默认统计维度：服务器 IP
          timeRange: '14d', // 默认时间范围：最近 14 天
          step: '1h', // 默认时间步长：1 小时
          useLocalTime: false // 是否使用局部时间范围（而非全局时间范围）
        },

        // 桑基图
        sankeyChart: null,
        sankeyLoading: false,
        sankeyData: {},
        sankeyForm: {
          timeRange: '1d', // 默认时间范围：最近 24 小时
          dbLimit: 5, // 默认数据库数量限制
          clientLimit: 10, // 默认客户端数量限制
          useLocalTime: false // 是否使用局部时间范围（而非全局时间范围）
        },

        // 全局时间范围和自动刷新
        globalTimeRange: '7d', // 全局时间范围：默认最近 7 天
        autoRefresh: false, // 自动刷新开关
        refreshTimer: null // 自动刷新定时器
      }
    },
    computed: {
      // 计算属性
    },
    mounted() {
      // 初始化图表
      this.initCharts()
      this.initTimeSeriesChart()
      this.initSankeyChart()

      // 获取数据
      this.fetchData()
      this.fetchTimeSeriesData()
      this.fetchSankeyData()

      // 窗口大小变化时重新调整图表大小
      window.addEventListener('resize', this.resizeCharts)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.resizeCharts)
      if (this.actionTypeChart) {
        this.actionTypeChart.dispose()
      }
      if (this.clientUserChart) {
        this.clientUserChart.dispose()
      }
      if (this.timeSeriesChart) {
        this.timeSeriesChart.dispose()
      }
      if (this.sankeyChart) {
        this.sankeyChart.dispose()
      }

      // 清除自动刷新定时器
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    methods: {
      // 初始化图表
      initCharts() {
        // 初始化操作类型玫瑰图
        this.initActionTypeChart()

        // 初始化客户端和用户柱状图
        this.initClientUserChart()
      },

      // 初始化操作类型玫瑰图
      initActionTypeChart() {
        if (this.$refs.actionTypeChart) {
          this.actionTypeChart = echarts.init(this.$refs.actionTypeChart)

          const option = {
            title: {
              text: '操作类型分布',
              left: 'center'
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            // 去掉图例展示
            legend: {
              show: false
            },
            series: [
              {
                name: '操作类型',
                type: 'pie',
                radius: ['20%', '80%'],
                center: ['50%', '50%'],
                roseType: 'area', // 南丁格尔玫瑰图特性
                itemStyle: {
                  borderRadius: 5 // 添加圆角效果
                },
                label: {
                  show: true,
                  position: 'outside',
                  formatter: '{b}\n{d}%', // 显示名称和百分比
                  textStyle: {
                    fontSize: 12,
                    fontWeight: 'bold'
                  },
                  distanceToLabelLine: 5
                },
                emphasis: {
                  label: {
                    fontSize: '15',
                    fontWeight: 'bold'
                  },
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                labelLine: {
                  smooth: 0.2,
                  length: 10,
                  length2: 20
                },
                data: []
              }
            ]
          }

          this.actionTypeChart.setOption(option)
        }
      },

      // 初始化客户端和用户柱状图
      initClientUserChart() {
        if (this.$refs.clientUserChart) {
          this.clientUserChart = echarts.init(this.$refs.clientUserChart)

          const option = {
            title: {
              text: '客户端和用户访问对比',
              left: 'center',
              top: 0,
              textStyle: {
                fontSize: 14
              }
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              confine: false, // 允许tooltip超出图表区域
              padding: [10, 15], // 增加内边距
              extraCssText: 'min-width:250px; max-width:400px;', // 设置最小和最大宽度
              formatter: function(params) {
                // 直接使用坐标轴值，它已经包含了客户端和用户信息
                let tooltip = '<div style="font-weight:bold;margin-bottom:8px;font-size:14px;">' + params[0].axisValue + '</div>';

                // 如果坐标轴值包含分隔符，说明是客户端和用户的组合
                if (params[0].axisValue.includes(' / ')) {
                  const parts = params[0].axisValue.split(' / ');
                  tooltip += '<div style="margin-bottom:5px;">客户端: <b style="color:#5470c6;">' + parts[0] + '</b></div>';
                  tooltip += '<div style="margin-bottom:5px;">用户: <b style="color:#91cc75;">' + parts[1] + '</b></div>';
                }

                tooltip += '<div style="margin-top:8px;border-top:1px solid #eee;padding-top:8px;">';

                params.forEach(param => {
                  tooltip += '<div style="margin-bottom:5px;">' + param.marker + ' ' +
                             '<span style="display:inline-block;min-width:60px;">' + param.seriesName + ':</span> ' +
                             '<b style="color:' + param.color + ';">' + param.value.toLocaleString() + '</b></div>';
                });

                tooltip += '</div>';
                return tooltip;
              }
            },
            legend: {
              data: ['客户端', '用户'],
              top: 25,
              textStyle: {
                fontSize: 12
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              top: 60,
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: [],
              axisLabel: {
                interval: 0,
                rotate: 30,
                fontSize: 10,
                lineHeight: 12,
                formatter: function (value) {
                  // 已经在数据处理时进行了截断，这里直接返回
                  return value;
                }
              },
              axisTick: {
                alignWithLabel: true
              }
            },
            yAxis: {
              type: 'value',
              name: '访问次数',
              nameTextStyle: {
                fontSize: 12
              },
              axisLabel: {
                fontSize: 10,
                formatter: function(value) {
                  if (value >= 1000000) {
                    return (value / 1000000).toFixed(1) + 'M';
                  } else if (value >= 1000) {
                    return (value / 1000).toFixed(1) + 'K';
                  }
                  return value;
                }
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed'
                }
              }
            },
            series: [
              {
                name: '客户端',
                type: 'bar',
                barMaxWidth: 30,
                itemStyle: {
                  color: '#5470c6'
                },
                emphasis: {
                  itemStyle: {
                    color: '#3a56b4'
                  }
                },
                data: [],
                markPoint: {
                  symbolSize: 60,
                  data: [
                    { type: 'max', name: '最大值' }
                  ],
                  label: {
                    fontSize: 12
                  }
                },
                markLine: {
                  data: [
                    { type: 'average', name: '平均值' }
                  ],
                  label: {
                    fontSize: 12,
                    position: 'middle',
                    formatter: '平均: {c}'
                  },
                  lineStyle: {
                    color: '#5470c6',
                    type: 'dashed'
                  }
                }
              },
              {
                name: '用户',
                type: 'bar',
                barMaxWidth: 30,
                itemStyle: {
                  color: '#91cc75'
                },
                emphasis: {
                  itemStyle: {
                    color: '#67a84d'
                  }
                },
                data: [],
                markPoint: {
                  symbolSize: 60,
                  data: [
                    { type: 'max', name: '最大值' }
                  ],
                  label: {
                    fontSize: 12
                  }
                },
                markLine: {
                  data: [
                    { type: 'average', name: '平均值' }
                  ],
                  label: {
                    fontSize: 12,
                    position: 'middle',
                    formatter: '平均: {c}'
                  },
                  lineStyle: {
                    color: '#91cc75',
                    type: 'dashed'
                  }
                }
              }
            ]
          }

          this.clientUserChart.setOption(option)
        }
      },

      // 调整图表大小
      resizeCharts() {
        if (this.actionTypeChart) {
          this.actionTypeChart.resize()
        }
        if (this.clientUserChart) {
          this.clientUserChart.resize()
        }
        if (this.timeSeriesChart) {
          this.timeSeriesChart.resize()
        }
        if (this.sankeyChart) {
          this.sankeyChart.resize()
        }
      },

      // 获取基础统计数据
      fetchData() {
        this.loading = true

        // 获取字段统计信息
        this.fetchFieldStats()
      },

      // 获取字段统计信息
      fetchFieldStats() {
        // 构建查询参数
        const query = `stream:"AUDITLOG_ORACLE_USER"`
        const fields = [
          'after.ACTION_NAME',
          'after.DBIP',
          'after.OWNER',
          'after.SERVERNAME',
          'after.USERHOST',
          'after.USERNAME'
        ]

        // 调用后端 facets API 获取字段统计信息
        getFacets(query, this.globalTimeRange, fields, 10)
          .then(response => {
            if (response.code === 200) {
              console.log('Received field stats data:', response.data)
              this.fieldStats = response.data

              // 处理字段统计数据
              this.processFieldStats(response.data)
            } else {
              console.error('获取字段统计信息失败:', response.msg)
              this.$message.error('获取字段统计信息失败: ' + response.msg)
            }
          })
          .catch(error => {
            console.error('获取字段统计信息失败:', error)
            this.$message.error('获取字段统计信息失败: ' + (error.message || error))
          })
          .finally(() => {
            this.loading = false
          })
      },

      // 处理字段统计数据
      processFieldStats(data) {
        if (!data || !data.facets) {
          return
        }

        // 提取热门操作类型 (after.ACTION_NAME)
        const actionTypeStats = data.facets.find(item => item.fieldName === 'after.ACTION_NAME')
        if (actionTypeStats && actionTypeStats.values) {
          this.topActionTypes = actionTypeStats.values.map(item => ({
            fieldValue: item.fieldValue,
            hits: item.hits
          }))

          // 计算数据操作数
          const dataOperations = actionTypeStats.values.filter(item =>
            ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'TRUNCATE TABLE'].includes(item.fieldValue))
          const dataHits = dataOperations.reduce((sum, item) => sum + item.hits, 0)
          this.statItems[2].value = this.formatNumber(dataHits)

          // 更新操作类型玫瑰图
          this.updateActionTypeChart()
        }

        // 提取热门用户 (after.USERNAME)
        const userStats = data.facets.find(item => item.fieldName === 'after.USERNAME')
        if (userStats && userStats.values) {
          this.topUsers = userStats.values.map(item => ({
            fieldValue: item.fieldValue,
            hits: item.hits
          }))
        }

        // 提取热门客户端 (after.USERHOST)
        const hostStats = data.facets.find(item => item.fieldName === 'after.USERHOST')
        if (hostStats && hostStats.values) {
          this.topHosts = hostStats.values.map(item => ({
            fieldValue: item.fieldValue,
            hits: item.hits
          }))

          // 更新客户端和用户柱状图
          this.updateClientUserChart()
        }

        // 提取热门数据库 (after.SERVERNAME)
        const dbStats = data.facets.find(item => item.fieldName === 'after.SERVERNAME')
        if (dbStats && dbStats.values) {
          this.topDatabases = dbStats.values.map(item => ({
            fieldValue: item.fieldValue,
            hits: item.hits
          }))

          // 计算接入数据库数
          this.statItems[0].value = this.formatNumber(dbStats.values.length)
        }

        // 提取热门数据库服务器 (after.DBIP)
        const dbServerStats = data.facets.find(item => item.fieldName === 'after.DBIP')
        if (dbServerStats && dbServerStats.values) {
          this.topDbServers = dbServerStats.values.map(item => ({
            fieldValue: item.fieldValue,
            hits: item.hits
          }))
        }

        // 提取热门表所有者 (after.OWNER)
        const ownerStats = data.facets.find(item => item.fieldName === 'after.OWNER')
        if (ownerStats && ownerStats.values) {
          this.topOwners = ownerStats.values.map(item => ({
            fieldValue: item.fieldValue,
            hits: item.hits
          }))
        }

        // 计算登录操作数
        const loginOperations = actionTypeStats ? actionTypeStats.values.filter(item =>
          ['LOGON', 'LOGOFF', 'LOGOFF BY CLEANUP'].includes(item.fieldValue)) : []
        const loginHits = loginOperations.reduce((sum, item) => sum + item.hits, 0)
        this.statItems[1].value = this.formatNumber(loginHits)

        // 获取今日和昨日的统计数据
        this.fetchTodayAndYesterdayStats()
      },

      // 获取今日和昨日的统计数据
      fetchTodayAndYesterdayStats() {

      },

      // 初始化时间序列图表
      initTimeSeriesChart() {
        if (this.timeSeriesChart) {
          this.timeSeriesChart.dispose()
        }

        this.timeSeriesChart = echarts.init(this.$refs.timeSeriesChart)

        // 设置图表基本配置
        const option = {
          title: {
            text: 'Oracle 日志访问时间序列统计',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            },
            subtext: '按' + this.getDimensionLabel(this.timeSeriesForm.dimension) + '统计',
            subtextStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            data: [],
            type: 'scroll',
            orient: 'horizontal',
            top: 50,
            textStyle: {
              fontSize: 12
            },
            pageButtonItemGap: 5,
            pageButtonGap: 5,
            pageButtonPosition: 'end',
            pageFormatter: '{current}/{total}',
            pageIconColor: '#2196F3',
            pageIconInactiveColor: '#aaa',
            pageIconSize: 15,
            pageTextStyle: {
              color: '#333'
            }
          },
          toolbox: {
            feature: {
              dataZoom: {
                yAxisIndex: 'none',
                title: {
                  zoom: '区域缩放',
                  back: '还原'
                }
              },
              saveAsImage: {
                title: '保存为图片'
              },
              restore: {
                title: '还原'
              }
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            },
            {
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: 100,
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisLabel: {
              formatter: function (value) {
                const date = new Date(value)
                return (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':00'
              },
              rotate: 0,
              margin: 8
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#eee'],
                type: 'dashed'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '访问次数',
            axisLabel: {
              formatter: function(value) {
                if (value >= 1000000) {
                  return (value / 1000000).toFixed(1) + 'M';
                } else if (value >= 1000) {
                  return (value / 1000).toFixed(1) + 'K';
                }
                return value;
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['#eee'],
                type: 'dashed'
              }
            }
          },
          series: []
        }

        this.timeSeriesChart.setOption(option)
        this.timeSeriesChart.resize()
      },

      // 获取时间序列数据
      fetchTimeSeriesData() {
        this.timeSeriesLoading = true

        // 更新图表标题
        if (this.timeSeriesChart) {
          let subtitle = '按' + this.getDimensionLabel(this.timeSeriesForm.dimension) + '统计'

          // 添加时间范围信息
          let timeRangeText = ''
          switch (this.timeSeriesForm.timeRange) {
            case '1d':
              timeRangeText = '最近 24 小时'
              break
            case '3d':
              timeRangeText = '最近 3 天'
              break
            case '7d':
              timeRangeText = '最近 7 天'
              break
            case '14d':
              timeRangeText = '最近 14 天'
              break
            case '30d':
              timeRangeText = '最近 30 天'
              break
            default:
              timeRangeText = this.timeSeriesForm.timeRange
          }
          subtitle += ' (' + timeRangeText + ')'

          this.timeSeriesChart.setOption({
            title: {
              subtext: subtitle,
              subtextStyle: {
                fontSize: 12,
                color: '#666'
              }
            }
          })
        }

        // 如果没有使用局部时间范围，则使用全局时间范围
        if (!this.timeSeriesForm.useLocalTime) {
          this.timeSeriesForm.timeRange = this.globalTimeRange
        }

        getOracleTimeSeriesStats(
          'all',
          this.timeSeriesForm.dimension,
          this.timeSeriesForm.timeRange,
          this.timeSeriesForm.step
        )
          .then(response => {
            if (response.code === 200) {
              this.updateTimeSeriesChart(response.data)
            } else {
              this.$message.error('获取时间序列数据失败: ' + response.msg)
            }
          })
          .catch(error => {
            console.error('获取时间序列数据失败:', error)
            this.$message.error('获取时间序列数据失败: ' + (error.message || error))
          })
          .finally(() => {
            this.timeSeriesLoading = false
          })
      },

      // 更新时间序列图表
      updateTimeSeriesChart(data) {
        console.log('Time series data:', data)

        // 处理可能的字符串响应
        let jsonData = data
        if (typeof data === 'string') {
          try {
            jsonData = JSON.parse(data)
          } catch (e) {
            console.error('解析时间序列数据失败:', e)
            this.$message.warning('无效的时间序列数据格式')
            return
          }
        }

        // 检查数据格式
        if (!jsonData || !jsonData.status || jsonData.status !== 'success' || !jsonData.data || !jsonData.data.result) {
          this.$message.warning('无效的时间序列数据')
          return
        }

        const results = jsonData.data.result
        if (results.length === 0) {
          this.$message.warning('没有找到符合条件的时间序列数据')
          return
        }

        // 准备图表数据
        const series = []
        const legendData = []

        // 最多显示 10 条数据，避免图表过于复杂
        const maxSeries = 10
        const topResults = results.slice(0, maxSeries)

        // 生成颜色数组
        const colors = [
          '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
          '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7074'
        ]

        // 处理每个结果
        topResults.forEach((result, index) => {
          // 获取维度名称
          const metric = result.metric || {}
          let dimensionName = metric[this.getDimensionFieldName(this.timeSeriesForm.dimension)] || 'unknown'

          // 获取数据点
          const values = result.values || []

          // 添加到图例
          legendData.push(dimensionName)

          // 准备数据点
          const seriesData = values.map(item => {
            // 时间戳转换为毫秒
            const timestamp = item[0] * 1000
            // 值转换为数字，处理可能的字符串值
            let value = 0
            if (typeof item[1] === 'string') {
              value = parseInt(item[1], 10) || 0
            } else {
              value = item[1] || 0
            }
            return [timestamp, value]
          })

          // 排序数据点，确保时间序列正确
          seriesData.sort((a, b) => a[0] - b[0])

          // 添加数据系列
          series.push({
            name: dimensionName,
            type: 'line',
            smooth: 0.2,
            symbol: 'emptyCircle',
            symbolSize: 6,
            sampling: 'average',
            itemStyle: {
              color: colors[index % colors.length]
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {
              opacity: 0.1
            },
            emphasis: {
              focus: 'series',
              itemStyle: {
                borderWidth: 2
              }
            },
            data: seriesData
          })
        })

        // 更新图表
        this.timeSeriesChart.setOption({
          legend: {
            data: legendData
          },
          series: series
        })
      },

      // 初始化桑基图
      initSankeyChart() {
        if (this.sankeyChart) {
          this.sankeyChart.dispose()
        }

        this.sankeyChart = echarts.init(this.$refs.sankeyChart)

        // 设置图表基本配置
        const option = {
          title: {
            text: 'Oracle 日志数据流向统计',
            subtext: '客户端主机名 -> 数据库服务器 IP -> 数据库服务名 (最近 24 小时)',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
            formatter: function(params) {
              if (params.dataType === 'node') {
                // 节点提示信息
                return params.name;
              } else {
                // 连接提示信息
                return params.data.source + ' -> ' + params.data.target + '<br/>'
                  + '访问次数: ' + params.data.value;
              }
            }
          },
          series: [
            {
              type: 'sankey',
              data: [],
              links: [],
              emphasis: {
                focus: 'adjacency'
              },
              lineStyle: {
                color: 'gradient',
                curveness: 0.5
              }
            }
          ]
        }

        this.sankeyChart.setOption(option)
        this.sankeyChart.resize()
      },

      // 获取桑基图数据
      fetchSankeyData() {
        this.sankeyLoading = true

        // 更新图表标题
        if (this.sankeyChart) {
          let timeRangeText = ''
          switch (this.sankeyForm.timeRange) {
            case '1d':
              timeRangeText = '最近 24 小时'
              break
            case '3d':
              timeRangeText = '最近 3 天'
              break
            case '7d':
              timeRangeText = '最近 7 天'
              break
            default:
              timeRangeText = this.sankeyForm.timeRange
          }

          this.sankeyChart.setOption({
            title: {
              subtext: '客户端主机名 -> 数据库服务器 IP -> 数据库服务名 (' + timeRangeText + ')'
            }
          })
        }

        // 如果没有使用局部时间范围，则使用全局时间范围
        if (!this.sankeyForm.useLocalTime) {
          this.sankeyForm.timeRange = this.globalTimeRange === '14d' || this.globalTimeRange === '30d' ? '7d' : this.globalTimeRange
        }

        getOracleDataFlowStats(
          this.sankeyForm.timeRange,
          this.sankeyForm.dbLimit,
          this.sankeyForm.clientLimit
        )
          .then(response => {
            if (response.code === 200) {
              this.updateSankeyChart(response.data)
            } else {
              this.$message.error('获取数据流向统计数据失败: ' + response.msg)
            }
          })
          .catch(error => {
            console.error('获取数据流向统计数据失败:', error)
            this.$message.error('获取数据流向统计数据失败: ' + (error.message || error))
          })
          .finally(() => {
            this.sankeyLoading = false
          })
      },

      // 更新桑基图
      updateSankeyChart(data) {
        console.log('Sankey data:', data)

        if (!data || !data.dbServers || !data.clientLinks || !data.dbServices || !data.dbServiceLinks) {
          this.$message.warning('无效的数据流向统计数据')
          return
        }

        const { dbServers, dbServices, clientLinks, dbServiceLinks } = data

        if (dbServers.length === 0 || clientLinks.length === 0) {
          this.$message.warning('没有找到符合条件的数据流向统计数据')
          return
        }

        // 准备桑基图数据
        const nodes = []
        const sankeyLinks = []

        // 添加客户端节点
        const clientHosts = new Set()
        clientLinks.forEach(link => {
          clientHosts.add(link.source)
        })

        clientHosts.forEach(host => {
          nodes.push({
            name: host,
            itemStyle: {
              color: '#91cc75' // 客户端节点颜色
            }
          })
        })

        // 添加数据库服务器节点
        dbServers.forEach(server => {
          nodes.push({
            name: server.ip,
            itemStyle: {
              color: '#5470c6' // 数据库服务器节点颜色
            }
          })
        })

        // 添加数据库服务节点
        dbServices.forEach(service => {
          nodes.push({
            name: service.name,
            itemStyle: {
              color: '#ee6666' // 数据库服务节点颜色
            }
          })
        })

        // 添加客户端到数据库服务器的连接
        clientLinks.forEach(link => {
          sankeyLinks.push({
            source: link.source,
            target: link.target,
            value: link.value
          })
        })

        // 添加数据库服务器到数据库服务的连接
        dbServiceLinks.forEach(link => {
          sankeyLinks.push({
            source: link.source,
            target: link.target,
            value: link.value
          })
        })

        // 更新图表
        this.sankeyChart.setOption({
          series: [
            {
              data: nodes,
              links: sankeyLinks
            }
          ]
        })
      },

      // 处理全局时间范围变化
      handleGlobalTimeRangeChange() {
        // 直接重置所有局部时间范围，不需要确认
        // 重置局部时间范围标志
        this.timeSeriesForm.useLocalTime = false
        this.sankeyForm.useLocalTime = false

        // 更新时间范围
        this.timeSeriesForm.timeRange = this.globalTimeRange
        this.sankeyForm.timeRange = this.globalTimeRange === '14d' || this.globalTimeRange === '30d' ? '7d' : this.globalTimeRange

        // 刷新所有数据
        this.refreshAllData()
      },

      // 处理时间序列统计的时间范围变化
      handleTimeSeriesTimeRangeChange() {
        // 标记使用局部时间范围
        this.timeSeriesForm.useLocalTime = true

        // 刷新时间序列数据
        this.fetchTimeSeriesData()
      },

      // 处理桑基图的时间范围变化
      handleSankeyTimeRangeChange() {
        // 标记使用局部时间范围
        this.sankeyForm.useLocalTime = true

        // 刷新桑基图数据
        this.fetchSankeyData()
      },

      // 处理自动刷新开关变化
      handleAutoRefreshChange() {
        if (this.autoRefresh) {
          // 开启自动刷新，每 60 秒刷新一次
          this.refreshTimer = setInterval(() => {
            this.refreshAllData()
          }, 60000) // 60秒 = 60000毫秒

          this.$message.success('自动刷新已开启，每 60 秒刷新一次')
        } else {
          // 关闭自动刷新
          if (this.refreshTimer) {
            clearInterval(this.refreshTimer)
            this.refreshTimer = null
          }

          this.$message.info('自动刷新已关闭')
        }
      },

      // 刷新所有数据
      refreshAllData() {
        // 刷新基本统计数据
        this.fetchData()

        // 刷新时间序列数据
        this.fetchTimeSeriesData()

        // 刷新桑基图数据
        this.fetchSankeyData()
      },

      // 格式化数字，添加千位分隔符
      formatNumber(num) {
        if (num === null || num === undefined) return '0'
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },

      // 获取维度标签
      getDimensionLabel(dimension) {
        switch (dimension) {
          case 'server_ip':
            return '服务器 IP'
          case 'db_name':
            return '数据库服务名'
          case 'client_host':
            return '客户端主机名'
          case 'client_user':
            return '客户端用户名'
          default:
            return '未知维度'
        }
      },

      // 获取维度字段名称
      getDimensionFieldName(dimension) {
        switch (dimension) {
          case 'server_ip':
            return 'after.DBIP'
          case 'db_name':
            return 'after.SERVERNAME'
          case 'client_host':
            return 'after.USERHOST'
          case 'client_user':
            return 'after.USERNAME'
          default:
            return ''
        }
      },

      // 截断字符串，超过指定长度时添加省略号
      truncateString(str, maxLength) {
        if (!str) return '';
        if (str.length <= maxLength) return str;
        return str.substring(0, maxLength) + '...';
      },

      // 更新操作类型玫瑰图
      updateActionTypeChart() {
        if (!this.actionTypeChart) return

        // 准备数据
        const chartData = this.topActionTypes.slice(0, 10).map(item => ({
          name: item.fieldValue,
          value: item.hits
        }))

        // 更新图表
        this.actionTypeChart.setOption({
          series: [{
            data: chartData
          }]
        })
      },

      // 更新客户端和用户柱状图
      updateClientUserChart() {
        if (!this.clientUserChart || !this.topHosts.length || !this.topUsers.length) return

        // 取前8个客户端和用户
        const topHosts = this.topHosts.slice(0, 8)
        const topUsers = this.topUsers.slice(0, 8)

        // 准备客户端和用户数据
        const hostData = [];
        const userData = [];
        const displayNames = [];

        // 计算客户端和用户的平均值
        let hostTotal = 0;
        let userTotal = 0;

        // 使用索引直接配对客户端和用户
        const maxLength = Math.min(8, Math.max(topHosts.length, topUsers.length));

        for (let i = 0; i < maxLength; i++) {
          // 客户端数据
          if (i < topHosts.length) {
            const hostValue = topHosts[i].hits;
            hostData.push(hostValue);
            hostTotal += hostValue;

            // 用户数据
            if (i < topUsers.length) {
              const userValue = topUsers[i].hits;
              userData.push(userValue);
              userTotal += userValue;

              // 组合客户端和用户名称
              const hostName = this.truncateString(topHosts[i].fieldValue, 8);
              const userName = this.truncateString(topUsers[i].fieldValue, 8);
              displayNames.push(hostName + ' / ' + userName);
            } else {
              userData.push(0);
              displayNames.push(this.truncateString(topHosts[i].fieldValue, 15));
            }
          } else if (i < topUsers.length) {
            // 只有用户数据
            hostData.push(0);
            const userValue = topUsers[i].hits;
            userData.push(userValue);
            userTotal += userValue;
            displayNames.push(this.truncateString(topUsers[i].fieldValue, 15));
          }
        }

        // 计算平均值
        const hostAvg = displayNames.length > 0 ? Math.round(hostTotal / displayNames.length) : 0;
        const userAvg = displayNames.length > 0 ? Math.round(userTotal / displayNames.length) : 0;

        // 更新图表
        this.clientUserChart.setOption({
          xAxis: {
            data: displayNames
          },
          series: [
            {
              name: '客户端',
              data: hostData,
              markLine: {
                data: [
                  {
                    type: 'average',
                    name: '平均值',
                    lineStyle: {
                      color: '#5470c6',
                      type: 'dashed'
                    },
                    label: {
                      formatter: '平均: ' + this.formatNumber(hostAvg)
                    }
                  }
                ]
              },
              markPoint: {
                data: [
                  {
                    type: 'max',
                    name: '最大值',
                    label: {
                      formatter: '{c}'
                    }
                  }
                ]
              }
            },
            {
              name: '用户',
              data: userData,
              markLine: {
                data: [
                  {
                    type: 'average',
                    name: '平均值',
                    lineStyle: {
                      color: '#91cc75',
                      type: 'dashed'
                    },
                    label: {
                      formatter: '平均: ' + this.formatNumber(userAvg)
                    }
                  }
                ]
              },
              markPoint: {
                data: [
                  {
                    type: 'max',
                    name: '最大值',
                    label: {
                      formatter: '{c}'
                    }
                  }
                ]
              }
            }
          ]
        })
      },



      // 生成随机颜色
      getRandomColor() {
        const colors = [
          '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
          '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7074'
        ]
        return colors[Math.floor(Math.random() * colors.length)]
      }
    }
  }
  </script>

  <style scoped>
  @import '../../../assets/styles/audit-overview.css';

  /* Oracle特有样式 */
  .action-type-chart {
    width: 100%;
    height: 350px;
  }

  .client-user-chart {
    width: 100%;
    height: 350px;
  }
  </style>
