package com.wiwj.securio.agent.config;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 日志处理规则配置对象
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public class LogProcessingRules implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 规则类型 */
    private String type;

    /** 规则名称 */
    private String name;

    /** 替换占位符 */
    @JsonProperty("replace_placeholder")
    private String replacePlaceholder;

    /** 匹配模式 */
    private String pattern;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getReplacePlaceholder() {
        return replacePlaceholder;
    }

    public void setReplacePlaceholder(String replacePlaceholder) {
        this.replacePlaceholder = replacePlaceholder;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }
}
