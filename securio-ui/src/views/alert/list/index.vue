<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <!-- 添加统计说明 -->
      <el-col :span="24" class="stats-header">
        <div class="stats-info">
          <span class="stats-title">告警统计概览</span>
          <span class="stats-time">最后更新：{{ lastUpdateTime }}</span>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="refreshStatistics">刷新</el-button>
        </div>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card stat-card-total">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.total }}</div>
              <div class="stat-label">总告警数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card stat-card-critical">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-close"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.critical }}</div>
              <div class="stat-label">严重告警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card stat-card-pending">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card stat-card-resolved">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.resolved }}</div>
              <div class="stat-label">已解决</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="search-form">
        <el-form-item label="告警标题" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入告警标题" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="告警源类型" prop="sourceType">
          <el-select v-model="queryParams.sourceType" placeholder="请选择告警源类型" clearable style="width: 120px">
            <el-option label="牧云" value="muyun" />
            <el-option label="网宿" value="wangsu" />
            <el-option label="夜莺(N9E)" value="n9e" />
            <el-option label="Grafana" value="grafana" />
            <el-option label="Prometheus" value="prometheus" />
            <el-option label="Zabbix" value="zabbix" />
            <el-option label="Zeek" value="zeek" />
            <el-option label="Suricata" value="suricata" />
          </el-select>
        </el-form-item>
        <el-form-item label="严重程度" prop="severity">
          <el-select v-model="queryParams.severity" placeholder="请选择严重程度" clearable style="width: 120px">
            <el-option label="严重" value="critical" />
            <el-option label="高危" value="high" />
            <el-option label="中危" value="medium" />
            <el-option label="低危" value="low" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择告警状态" clearable style="width: 120px">
            <el-option label="触发" value="triggered" />
            <el-option label="确认" value="acknowledged" />
            <el-option label="已解决" value="resolved" />
            <el-option label="沉默" value="suppressed" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务名称" prop="serviceName">
          <el-input v-model="queryParams.serviceName" placeholder="请输入服务名称" clearable style="width: 150px">
            <template slot="prepend">
              <el-tooltip content="通过tags.service字段搜索" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="源IP" prop="sourceIp">
          <el-input v-model="queryParams.sourceIp" placeholder="请输入源IP地址" clearable style="width: 140px" />
        </el-form-item>
        <el-form-item label="目标IP" prop="targetIp">
          <el-input v-model="queryParams.targetIp" placeholder="请输入目标IP地址" clearable style="width: 140px" />
        </el-form-item>
        <el-form-item label="标签搜索" prop="tagSearch">
          <!-- 切换按钮 -->
          <div class="tag-search-header">
            <el-button-group class="search-mode-toggle">
              <el-button
                :type="tagSearchMode === 'simple' ? 'primary' : 'default'"
                size="mini"
                @click="tagSearchMode = 'simple'"
              >
                简单搜索
              </el-button>
              <el-button
                :type="tagSearchMode === 'advanced' ? 'primary' : 'default'"
                size="mini"
                @click="tagSearchMode = 'advanced'"
              >
                高级搜索
              </el-button>
            </el-button-group>
          </div>

          <!-- 简单模式 -->
          <div v-if="tagSearchMode === 'simple'" class="simple-search">
            <el-input v-model="queryParams.tagSearch" placeholder="支持多标签：environment:prod AND service:api" clearable style="width: 320px">
              <template slot="prepend">
                <el-select v-model="tagSearchType" style="width: 80px">
                  <el-option label="包含" value="contains" />
                  <el-option label="精确" value="exact" />
                </el-select>
              </template>
              <template slot="append">
                <el-popover placement="bottom" width="400" trigger="click">
                  <div class="tag-help">
                    <h4>标签搜索说明</h4>
                    <p><strong>格式：</strong> 标签名:标签值</p>

                    <div class="multi-tag-info">
                      <h5>多标签搜索：</h5>
                      <p>• <code>environment:production AND service:user-api</code></p>
                      <p>• <code>team:backend AND environment:staging</code></p>
                      <p>• 支持分隔符：<code>AND</code>, <code>OR</code>, <code>,</code>, <code>;</code></p>
                    </div>

                    <div class="tag-examples">
                      <h5>常用标签：</h5>
                      <div class="tag-categories">
                        <div class="tag-category">
                          <span class="category-name">环境：</span>
                          <el-tag size="mini" type="danger" class="example-tag" @click="selectTagExample('environment:production')">production</el-tag>
                          <el-tag size="mini" type="warning" class="example-tag" @click="selectTagExample('environment:staging')">staging</el-tag>
                          <el-tag size="mini" type="info" class="example-tag" @click="selectTagExample('environment:test')">test</el-tag>
                        </div>
                        <div class="tag-category">
                          <span class="category-name">服务：</span>
                          <el-tag size="mini" type="primary" class="example-tag" @click="selectTagExample('service:user-api')">user-api</el-tag>
                          <el-tag size="mini" type="primary" class="example-tag" @click="selectTagExample('service:order-service')">order-service</el-tag>
                          <el-tag size="mini" type="primary" class="example-tag" @click="selectTagExample('service:payment-gateway')">payment-gateway</el-tag>
                        </div>
                        <div class="tag-category">
                          <span class="category-name">团队：</span>
                          <el-tag size="mini" type="success" class="example-tag" @click="selectTagExample('team:backend')">backend</el-tag>
                          <el-tag size="mini" type="success" class="example-tag" @click="selectTagExample('team:frontend')">frontend</el-tag>
                          <el-tag size="mini" type="success" class="example-tag" @click="selectTagExample('team:devops')">devops</el-tag>
                        </div>
                      </div>
                    </div>

                    <div class="search-modes">
                      <h5>搜索模式：</h5>
                      <p><strong>包含：</strong> 标签值包含输入内容</p>
                      <p><strong>精确：</strong> 标签值完全匹配</p>
                    </div>
                  </div>
                  <el-button slot="reference" icon="el-icon-question" size="mini" circle></el-button>
                </el-popover>
              </template>
            </el-input>
          </div>

          <!-- 高级模式（可视化选择器） -->
          <div v-else class="advanced-search">
            <el-popover
              placement="bottom-start"
              width="450"
              trigger="click"
              v-model="showAdvancedSelector"
            >
              <div class="advanced-selector">
                <div class="selector-header">
                  <h4>标签选择器</h4>
                  <el-button size="mini" type="text" @click="clearAllTags">清空</el-button>
                </div>

                <div class="selector-body">
                  <el-tabs v-model="activeTagTab" type="card" size="small">
                    <el-tab-pane label="环境" name="environment">
                      <div class="tag-options">
                        <div
                          v-for="tag in environmentTags"
                          :key="tag.value"
                          class="tag-option"
                          :class="{ active: selectedAdvancedTags.includes(tag.value) }"
                          @click="toggleAdvancedTag(tag.value)"
                        >
                          <el-tag :type="tag.type" size="small">{{ tag.label }}</el-tag>
                          <span class="tag-description">{{ tag.description }}</span>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="服务" name="service">
                      <div class="tag-options">
                        <div
                          v-for="tag in serviceTags"
                          :key="tag.value"
                          class="tag-option"
                          :class="{ active: selectedAdvancedTags.includes(tag.value) }"
                          @click="toggleAdvancedTag(tag.value)"
                        >
                          <el-tag :type="tag.type" size="small">{{ tag.label }}</el-tag>
                          <span class="tag-description">{{ tag.description }}</span>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="团队" name="team">
                      <div class="tag-options">
                        <div
                          v-for="tag in teamTags"
                          :key="tag.value"
                          class="tag-option"
                          :class="{ active: selectedAdvancedTags.includes(tag.value) }"
                          @click="toggleAdvancedTag(tag.value)"
                        >
                          <el-tag :type="tag.type" size="small">{{ tag.label }}</el-tag>
                          <span class="tag-description">{{ tag.description }}</span>
                        </div>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>

                <div class="selector-footer">
                  <div class="selected-tags">
                    <span class="label">已选择：</span>
                    <el-tag
                      v-for="tag in selectedAdvancedTags"
                      :key="tag"
                      size="mini"
                      closable
                      @close="removeAdvancedTag(tag)"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                  <div class="actions">
                    <el-button size="mini" @click="showAdvancedSelector = false">取消</el-button>
                    <el-button size="mini" type="primary" @click="confirmAdvancedSelection">确定</el-button>
                  </div>
                </div>
              </div>

              <el-input
                slot="reference"
                :value="advancedDisplayValue"
                placeholder="点击选择多个标签"
                readonly
                clearable
                @clear="clearAllTags"
                style="width: 280px"
              >
                <template slot="append">
                  <el-button icon="el-icon-arrow-down" size="mini"></el-button>
                </template>
              </el-input>
            </el-popover>
          </div>
        </el-form-item>
        <el-form-item label="时间范围" prop="timeRange">
          <el-date-picker
            v-model="queryParams.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="toolbar-card" shadow="never">
      <el-row :gutter="10" class="toolbar-row">
        <el-col :span="12">
          <el-button-group>
            <el-button type="primary" icon="el-icon-refresh" @click="handleRefresh">刷新</el-button>
            <el-button type="success" icon="el-icon-check" @click="handleBatchResolve" :disabled="!hasSelection">批量处理</el-button>
            <el-button type="info" icon="el-icon-download" @click="handleExport">导出</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="12" class="toolbar-right">
          <el-tooltip content="自动刷新" placement="top">
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            ></el-switch>
          </el-tooltip>
          <el-divider direction="vertical"></el-divider>
          <el-tooltip content="显示/隐藏搜索" placement="top">
            <el-button icon="el-icon-search" circle @click="showSearch = !showSearch"></el-button>
          </el-tooltip>
          <el-tooltip content="列表设置" placement="top">
            <el-button icon="el-icon-setting" circle @click="handleColumnSetting"></el-button>
          </el-tooltip>
        </el-col>
      </el-row>
    </el-card>

    <!-- 告警列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="alertList"
        @selection-change="handleSelectionChange"
        class="alert-table"
        stripe
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="告警标题" prop="title" :show-overflow-tooltip="true" min-width="250">
          <template slot-scope="scope">
            <div class="alert-title">
              <el-link type="primary" @click="handleView(scope.row)" class="title-link">
                {{ scope.row.title }}
              </el-link>
              <div class="alert-meta">
                <el-tag v-if="scope.row.alertType" size="mini" type="info">{{ scope.row.alertType }}</el-tag>
                <span class="event-id" v-if="scope.row.eventId">#{{ scope.row.eventId }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="严重程度" prop="severity" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSeverityType(scope.row.severity)" size="small">
              {{ getSeverityText(scope.row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警源" prop="sourceType" width="120" align="center">
          <template slot-scope="scope">
            <div class="source-info">
              <el-tag :type="getSourceTypeColor(scope.row.sourceType)" size="small">
                {{ getSourceTypeName(scope.row.sourceType) }}
              </el-tag>
              <div class="source-detail" v-if="scope.row.sourceName">
                {{ scope.row.sourceName }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务/组件" prop="serviceName" width="150" align="center">
          <template slot-scope="scope">
            <div class="service-info">
              <div v-if="getServiceFromTags(scope.row.tags)" class="service-name">
                <el-tag size="mini" type="primary">{{ getServiceFromTags(scope.row.tags) }}</el-tag>
              </div>
              <div v-if="scope.row.ruleName" class="rule-name">{{ scope.row.ruleName }}</div>
              <div v-if="scope.row.groupName" class="group-name">{{ scope.row.groupName }}</div>
              <div v-if="!getServiceFromTags(scope.row.tags) && !scope.row.ruleName && !scope.row.groupName" class="no-service">-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="环境/位置" prop="environment" width="120" align="center">
          <template slot-scope="scope">
            <div class="location-info">
              <el-tag v-if="getEnvironmentFromTags(scope.row.tags)" size="mini" :type="getEnvironmentColor(getEnvironmentFromTags(scope.row.tags))">
                {{ getEnvironmentFromTags(scope.row.tags) }}
              </el-tag>
              <div v-if="scope.row.sourceIdent" class="source-ident">{{ scope.row.sourceIdent }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="标签" prop="tags" width="200" align="center">
          <template slot-scope="scope">
            <div class="tags-display">
              <template v-if="getTagsForDisplay(scope.row.tags).length > 0">
                <!-- 显示前3个标签 -->
                <el-tag
                  v-for="(tag, index) in getTagsForDisplay(scope.row.tags).slice(0, 3)"
                  :key="index"
                  size="mini"
                  :type="getTagTypeByKey(tag.key)"
                  class="tag-item"
                >
                  {{ tag.key }}:{{ tag.value }}
                </el-tag>
                <!-- 如果有更多标签，显示更多按钮 -->
                <el-popover
                  v-if="getTagsForDisplay(scope.row.tags).length > 3"
                  placement="top"
                  width="300"
                  trigger="hover"
                >
                  <div class="all-tags">
                    <el-tag
                      v-for="(tag, index) in getTagsForDisplay(scope.row.tags)"
                      :key="index"
                      size="mini"
                      :type="getTagTypeByKey(tag.key)"
                      class="tag-item"
                    >
                      {{ tag.key }}:{{ tag.value }}
                    </el-tag>
                  </div>
                  <el-tag slot="reference" size="mini" type="info" class="more-tags">
                    +{{ getTagsForDisplay(scope.row.tags).length - 3 }}
                  </el-tag>
                </el-popover>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="发生时间" prop="occurredAt" width="160" align="center">
          <template slot-scope="scope">
            <div class="time-info">
              <div class="occurred-time">{{ parseTime(scope.row.occurredAt) }}</div>
              <div class="time-ago">{{ getTimeAgo(scope.row.occurredAt) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="持续时间" prop="durationMs" width="120" align="center">
          <template slot-scope="scope">
            <div class="duration-info">
              <span :class="getDurationClass(scope.row.durationMs || calculateDuration(scope.row))">
                {{ formatDuration(scope.row.durationMs || calculateDuration(scope.row)) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="处理人" prop="resolvedBy" width="100" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.resolvedByName" class="resolver-info">
              <el-avatar :size="24" :src="getUserAvatar(scope.row.resolvedBy)">{{ scope.row.resolvedByName.charAt(0) }}</el-avatar>
              <span class="resolver-name">{{ scope.row.resolvedByName }}</span>
            </div>
            <span v-else class="no-resolver">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
            <el-button v-if="!isResolved(scope.row.status)" size="mini" type="text" icon="el-icon-check" @click="handleResolve(scope.row)">处理</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" style="color: #f56c6c;">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 告警详情抽屉 -->
    <el-drawer
      title="告警详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
    >
      <div class="alert-detail-drawer" v-if="currentAlert.id">
        <!-- 告警基本信息 -->
        <el-card class="detail-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag :type="getStatusType(currentAlert.status)" size="small">
              {{ getStatusText(currentAlert.status) }}
            </el-tag>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="告警标题">
              <span class="alert-title-text">{{ currentAlert.title }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="事件ID">
              <el-tag size="mini" type="info" v-if="currentAlert.eventId">#{{ currentAlert.eventId }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="严重程度">
              <el-tag :type="getSeverityType(currentAlert.severity)" size="small">
                {{ getSeverityText(currentAlert.severity) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="告警类型">
              <el-tag size="mini" type="info" v-if="currentAlert.alertType">{{ currentAlert.alertType }}</el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="告警源">
              <div>
                <el-tag :type="getSourceTypeColor(currentAlert.sourceType)" size="small">
                  {{ getSourceTypeName(currentAlert.sourceType) }}
                </el-tag>
                <el-tag v-if="currentAlert.sourceSubType" size="mini" type="info" style="margin-left: 5px;">
                  {{ currentAlert.sourceSubType }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="源实例">
              {{ currentAlert.sourceName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="规则名称">
              {{ currentAlert.ruleName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="组名称">
              {{ currentAlert.groupName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              <div class="alert-description">
                {{ currentAlert.description || '-' }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="处理类型" v-if="currentAlert.resolutionType">
              <el-tag size="small" :type="getResolutionTypeColor(currentAlert.resolutionType)">
                {{ getResolutionTypeText(currentAlert.resolutionType) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 告警元数据(原始数据) -->
        <el-card class="detail-card json-card" shadow="hover" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-document"></i> 原始数据
            </span>
            <div class="header-controls">
              <el-input
                v-if="currentAlert.rawData"
                placeholder="搜索字段..."
                v-model="jsonSearchQuery"
                prefix-icon="el-icon-search"
                clearable
                size="mini"
                style="width: 200px; margin-right: 10px;"
                @input="highlightJsonSearch"
              ></el-input>
              <el-dropdown size="mini" split-button type="primary" @command="handleJsonViewCommand">
                视图选项
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="expand">{{ rawDataExpanded ? '收起' : '展开全部' }}</el-dropdown-item>
                  <el-dropdown-item command="format">重新格式化</el-dropdown-item>
                  <el-dropdown-item command="tree" :disabled="!supportsTreeView">树形视图</el-dropdown-item>
                  <el-dropdown-item command="plain" :disabled="!supportsTreeView">普通视图</el-dropdown-item>
                  <el-dropdown-item divided command="copy">复制到剪贴板</el-dropdown-item>
                  <el-dropdown-item command="download">下载JSON文件</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div v-if="currentAlert.rawData" class="json-container">
            <div class="json-controls">
              <el-slider 
                v-model="jsonFontSize" 
                :min="10" 
                :max="18" 
                :step="1" 
                :format-tooltip="value => `${value}px`"
                @change="updateJsonStyles"
              ></el-slider>
              <div class="search-results" v-if="jsonSearchResults.length > 0">
                发现 {{ jsonSearchResults.length }} 处匹配
                <el-button-group class="search-navigation">
                  <el-button 
                    size="mini" 
                    icon="el-icon-arrow-up" 
                    :disabled="jsonSearchIndex <= 0"
                    @click="navigateJsonSearch('prev')"
                  ></el-button>
                  <el-button 
                    size="mini" 
                    icon="el-icon-arrow-down" 
                    :disabled="jsonSearchIndex >= jsonSearchResults.length - 1"
                    @click="navigateJsonSearch('next')"
                  ></el-button>
                </el-button-group>
                <span class="search-index" v-if="jsonSearchResults.length > 0">
                  {{ jsonSearchIndex + 1 }}/{{ jsonSearchResults.length }}
                </span>
              </div>
            </div>
            <div class="json-wrapper" :style="{ 'max-height': rawDataExpanded ? 'none' : '400px' }">
              <pre 
                ref="jsonViewer"
                class="raw-data-content" 
                :class="{ 
                  'expanded': rawDataExpanded,
                  'syntax-highlight': enableSyntaxHighlight,
                  'dark-theme': jsonDarkTheme
                }"
                :style="{ 'font-size': `${jsonFontSize}px` }"
                v-html="highlightedJson"
              ></pre>
            </div>
            <div class="json-footer">
              <el-switch
                v-model="enableSyntaxHighlight"
                active-text="语法高亮"
                inactive-text="普通文本"
                @change="updateJsonDisplay"
              ></el-switch>
              <el-switch
                v-model="jsonDarkTheme"
                active-text="暗色主题"
                inactive-text="亮色主题"
                @change="updateJsonDisplay"
              ></el-switch>
              <el-tooltip content="在新窗口中查看" placement="top">
                <el-button 
                  size="mini" 
                  icon="el-icon-full-screen" 
                  circle
                  @click="openJsonInNewWindow"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
          <div v-else class="empty-data">
            <i class="el-icon-document"></i>
            <p>暂无原始数据</p>
          </div>
        </el-card>
      </div>

      <!-- 抽屉底部操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="drawerVisible = false">关闭</el-button>
        <el-button v-if="!isResolved(currentAlert.status)" type="primary" @click="handleResolve(currentAlert)">处理告警</el-button>
        <el-button type="danger" @click="handleDelete(currentAlert)">删除告警</el-button>
      </div>
    </el-drawer>

    <!-- 查看告警详情对话框 -->
    <el-dialog
      title="告警详情"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="告警来源">{{ currentAlert.source }}</el-descriptions-item>
        <el-descriptions-item label="告警级别">
          <el-tag :type="getSeverityType(currentAlert.severity)">
            {{ currentAlert.severity }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警状态">
          <el-tag :type="getStatusType(currentAlert.status)">
            {{ currentAlert.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警时间">{{ currentAlert.alertTime }}</el-descriptions-item>
        <el-descriptions-item label="告警描述" :span="2">{{ currentAlert.description }}</el-descriptions-item>
        <el-descriptions-item label="告警详情" :span="2">{{ currentAlert.details }}</el-descriptions-item>
        <el-descriptions-item label="处理记录" :span="2" v-if="currentAlert.status !== 'pending'">
          {{ currentAlert.resolutionNote }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理告警对话框 -->
    <el-dialog
      title="处理告警"
      :visible.sync="resolveDialogVisible"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="resolve-alert-container">
        <!-- 告警信息摘要 -->
        <div class="alert-summary">
          <div class="alert-title">
            <span class="label">告警:</span>
            <span class="value">{{ currentAlert.title }}</span>
            <el-tag size="mini" :type="getSeverityType(currentAlert.severity)">
              {{ getSeverityText(currentAlert.severity) }}
            </el-tag>
          </div>
          <div class="alert-meta">
            <div class="meta-item">
              <span class="label">来源:</span>
              <el-tag size="mini" :type="getSourceTypeColor(currentAlert.sourceType)">
                {{ getSourceTypeName(currentAlert.sourceType) }}
              </el-tag>
            </div>
            <div class="meta-item">
              <span class="label">ID:</span>
              <span class="value">{{ currentAlert.id }}</span>
            </div>
            <div class="meta-item">
              <span class="label">时间:</span>
              <span class="value">{{ parseTime(currentAlert.occurredAt) }}</span>
            </div>
          </div>
        </div>
        
        <el-divider content-position="center">处理方式</el-divider>
        
        <el-form :model="resolveForm" label-width="100px">
          <el-form-item label="选择操作">
            <el-radio-group v-model="resolveForm.action" class="resolve-options">
              <el-radio-button label="resolve">
                <i class="el-icon-check"></i> 已解决
              </el-radio-button>
              <el-radio-button label="ignore">
                <i class="el-icon-close"></i> 忽略
              </el-radio-button>
              <el-radio-button label="postpone">
                <i class="el-icon-time"></i> 延后处理
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="处理模板" v-if="resolveForm.action === 'resolve'">
            <el-select v-model="selectedTemplate" placeholder="选择处理模板" @change="applyTemplate" style="width: 100%">
              <el-option label="已修复相关服务" value="fixed_service"></el-option>
              <el-option label="已重启服务解决" value="restart_service"></el-option>
              <el-option label="配置调整已解决" value="config_change"></el-option>
              <el-option label="网络问题已修复" value="network_fixed"></el-option>
              <el-option label="临时资源问题" value="resource_issue"></el-option>
              <el-option label="自定义..." value="custom"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item 
            label="延后时间" 
            v-if="resolveForm.action === 'postpone'"
          >
            <el-select v-model="resolveForm.postponeTime" placeholder="选择延后时间" style="width: 100%">
              <el-option label="1小时" value="1h"></el-option>
              <el-option label="4小时" value="4h"></el-option>
              <el-option label="1天" value="1d"></el-option>
              <el-option label="3天" value="3d"></el-option>
              <el-option label="1周" value="1w"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="处理说明">
            <el-input
              type="textarea"
              v-model="resolveForm.note"
              :rows="4"
              placeholder="请输入处理说明"
            />
          </el-form-item>
          
          <el-form-item label="关联问题" v-if="resolveForm.action === 'resolve'">
            <el-input placeholder="输入关联的JIRA/工单ID" v-model="resolveForm.ticketId">
              <template slot="prepend">
                <el-select v-model="resolveForm.ticketType" style="width: 100px">
                  <el-option label="JIRA" value="jira"></el-option>
                  <el-option label="工单" value="ticket"></el-option>
                  <el-option label="事件" value="incident"></el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="通知" v-if="resolveForm.action !== 'postpone'">
            <el-checkbox v-model="resolveForm.notify">发送处理结果通知</el-checkbox>
          </el-form-item>
          <el-form-item label="处理类型" v-if="resolveForm.action === 'resolve'">
            <el-select v-model="resolveForm.resolutionType" placeholder="选择处理类型" style="width: 100%">
              <el-option label="人工解决" value="manual_resolved"></el-option>
              <el-option label="服务重启" value="service_restart"></el-option>
              <el-option label="配置调整" value="config_change"></el-option>
              <el-option label="网络修复" value="network_fixed"></el-option>
              <el-option label="资源调整" value="resource_adjusted"></el-option>
              <el-option label="误报" value="false_positive"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="resolveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitResolve" :loading="resolveSubmitting">
          {{ getResolveActionText() }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Message, MessageBox } from 'element-ui'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import RightToolbar from '@/components/RightToolbar'
import request from '@/utils/request'

export default {
  name: "AlertList",
  components: {
    Pagination,
    RightToolbar
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 统计数据
      statistics: {
        total: 0,
        critical: 0,
        pending: 0,
        resolved: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        sourceType: null,
        severity: null,
        status: null,
        serviceName: null,
        sourceIp: null,
        targetIp: null,
        tagSearch: null,
        timeRange: []
      },
      // 标签搜索类型
      tagSearchType: 'contains',
      // 标签搜索模式
      tagSearchMode: 'simple', // simple | advanced
      // 高级选择器状态
      showAdvancedSelector: false,
      activeTagTab: 'environment',
      selectedAdvancedTags: [],
      // 标签示例
      tagExamples: [
        { label: 'environment:production', value: 'environment:production' },
        { label: 'environment:staging', value: 'environment:staging' },
        { label: 'service:user-api', value: 'service:user-api' },
        { label: 'service:order-service', value: 'service:order-service' },
        { label: 'team:backend', value: 'team:backend' },
        { label: 'team:frontend', value: 'team:frontend' },
        { label: 'severity:critical', value: 'severity:critical' },
        { label: 'monitor_system:grafana', value: 'monitor_system:grafana' }
      ],
      // 动态标签分类（从后端获取）
      environmentTags: [],
      serviceTags: [],
      teamTags: [],
      monitorTags: [],
      // 标签加载状态
      tagsLoading: false,
      // 数据列表
      alertList: [],
      // 选中的行
      selectedRows: [],
      // 自动刷新
      autoRefresh: false,
      refreshTimer: null,
      // 对话框控制
      dialogVisible: false,
      drawerVisible: false,
      resolveDialogVisible: false,
      currentAlert: {},
      resolveForm: {
        action: 'resolve',
        note: '',
        resolvedBy: '',
        resolvedByName: '',
        ticketType: 'jira',
        ticketId: '',
        postponeTime: '1h',
        notify: true,
        resolutionType: 'manual_resolved'
      },
      rawDataExpanded: false,
      jsonSearchQuery: '',
      jsonSearchResults: [],
      jsonSearchIndex: 0,
      enableSyntaxHighlight: true,
      jsonDarkTheme: false,
      jsonFontSize: 14,
      highlightedJson: '', // 将highlightedJson从computed属性改为data属性
      // 日期选择器选项
      pickerOptions: {
        shortcuts: [
          {
            text: '最近1小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '今天',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().toDateString());
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const end = new Date(new Date().toDateString());
              const start = new Date();
              start.setTime(end.getTime() - 3600 * 1000 * 24);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近3天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      selectedTemplate: '',
      resolveSubmitting: false
    };
  },
  computed: {
    // 是否有选中的行
    hasSelection() {
      return this.selectedRows.length > 0;
    },
    // 高级模式显示值
    advancedDisplayValue() {
      if (this.selectedAdvancedTags.length === 0) {
        return ''
      } else if (this.selectedAdvancedTags.length === 1) {
        return this.selectedAdvancedTags[0]
      } else {
        return `${this.selectedAdvancedTags.length} 个标签已选择`
      }
    },
    supportsTreeView() {
      return this.currentAlert.rawData && typeof this.currentAlert.rawData === 'object';
    }
  },
  watch: {
    // 监听当前告警变化，更新JSON显示
    'currentAlert.rawData': {
      handler(newVal) {
        if (newVal) {
          this.updateJsonDisplay();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getList();
    this.getStatistics();
    this.loadAvailableTags();
    
    // 初始化JSON显示
    this.$nextTick(() => {
      if (this.currentAlert && this.currentAlert.rawData) {
        this.updateJsonDisplay();
      }
    });
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },
  methods: {
    // 获取告警列表
    async getList() {
      this.loading = true
      try {
        const response = await request({
          url: '/alert/list',
          method: 'get',
          params: this.queryParams
        })
        this.alertList = response.rows || []
        this.total = response.total || 0
        // 更新统计数据
        this.updateStatistics()
      } catch (error) {
        console.error('获取告警列表失败:', error)
        this.$message.error('获取告警列表失败')
      }
      this.loading = false
    },

    // 获取统计数据
    async getStatistics() {
      try {
        const response = await request({
          url: '/alert/api/statistics',
          method: 'get'
        })
        this.statistics = response.data || {
          total: 0,
          critical: 0,
          pending: 0,
          resolved: 0
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },

    // 更新统计数据（基于当前列表数据）
    updateStatistics() {
      this.statistics.total = this.total
      this.statistics.critical = this.alertList.filter(item => item.severity === 'critical').length
      this.statistics.pending = this.alertList.filter(item => ['new', 'processing'].includes(item.status)).length
      this.statistics.resolved = this.alertList.filter(item => ['resolved', 'closed'].includes(item.status)).length
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: null,
        sourceType: null,
        severity: null,
        status: null,
        serviceName: null,
        sourceIp: null,
        targetIp: null,
        tagSearch: null,
        timeRange: []
      }
      this.tagSearchType = 'contains'
      this.handleQuery()
    },

    // 刷新
    handleRefresh() {
      this.getList()
      this.getStatistics()
    },

    // 切换自动刷新
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.getList()
          this.getStatistics()
        }, 30000) // 30秒刷新一次
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 批量处理告警
    handleBatchResolve() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要处理的告警')
        return
      }

      this.$confirm(`是否确认批量处理选中的 ${this.selectedRows.length} 条告警?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const ids = this.selectedRows.map(row => row.id)
          const response = await request({
            url: '/alert/api/batch-resolve',
            method: 'put',
            data: {
              ids: ids,
              resolvedBy: this.$store.state.user.id || 1,
              resolvedByName: this.$store.state.user.name || 'admin',
              note: '批量处理'
            }
          })
          if (response.code === 200) {
            this.$message.success('批量处理成功')
            this.getList()
          } else {
            this.$message.error(response.msg || '批量处理失败')
          }
        } catch (error) {
          console.error('批量处理失败:', error)
          this.$message.error('批量处理失败')
        }
      })
    },

    // 查看告警详情
    handleView(row) {
      this.currentAlert = { ...row }
      this.drawerVisible = true
    },

    // 关闭抽屉
    handleDrawerClose() {
      this.drawerVisible = false
      this.currentAlert = {}
    },

    // 处理告警
    handleResolve(row) {
      this.currentAlert = row
      this.resolveForm.action = 'resolve'
      this.resolveForm.note = ''
      this.resolveDialogVisible = true
    },

    // 重新打开告警
    handleReopen(row) {
      this.$confirm('是否确认重新打开该告警?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await request({
            url: `/alert/reopen/${row.id}`,
            method: 'put'
          })
          if (response.code === 200) {
            this.$message.success('告警已重新打开')
            this.getList()
          } else {
            this.$message.error(response.msg || '重开告警失败')
          }
        } catch (error) {
          console.error('重开告警失败:', error)
          this.$message.error('重开告警失败')
        }
      })
    },

    // 更多操作
    handleMoreAction(command, row) {
      switch (command) {
        case 'assign':
          this.handleAssign(row)
          break
        case 'note':
          this.handleAddNote(row)
          break
        case 'silence':
          this.handleSilence(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    },

    // 分配告警
    handleAssign(row) {
      this.$message.info('分配功能待实现')
    },

    // 添加备注
    handleAddNote(row) {
      this.$message.info('添加备注功能待实现')
    },

    // 静默告警
    handleSilence(row) {
      this.$message.info('静默功能待实现')
    },

    // 提交处理
    async submitResolve() {
      try {
        const response = await request({
          url: '/system/alert/resolve',
          method: 'post',
          data: {
            id: this.currentAlert.id,
            ...this.resolveForm
          }
        })
        if (response.code === 200) {
          this.showMessage('success', '处理成功')
          this.resolveDialogVisible = false
          this.getList()
        } else {
          this.showMessage('error', response.msg || '处理失败')
        }
      } catch (error) {
        console.error('处理告警失败:', error)
        this.showMessage('error', '处理告警失败')
      }
    },

    // 删除告警
    handleDelete(row) {
      this.showConfirm('确认删除该告警吗？', '警告').then(async () => {
        try {
          const response = await request({
            url: `/system/alert/${row.id}`,
            method: 'delete'
          })
          if (response.code === 200) {
            this.showMessage('success', '删除成功')
            this.getList()
          } else {
            this.showMessage('error', response.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除告警失败:', error)
          this.showMessage('error', '删除告警失败')
        }
      })
    },

    // 导出告警
    async handleExport() {
      try {
        const response = await request({
          url: '/system/alert/export',
          method: 'get',
          params: this.queryParams,
          responseType: 'blob'
        })
        const blob = new Blob([response.data])
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = '告警列表.xlsx'
        link.click()
        window.URL.revokeObjectURL(link.href)
        this.showMessage('success', '导出成功')
      } catch (error) {
        console.error('导出告警失败:', error)
        this.showMessage('error', '导出告警失败')
      }
    },

    // 生成测试告警数据
    generateTestAlert() {
      const sources = ['system', 'security', 'network']
      const severities = ['critical', 'warning', 'info']
      const source = sources[Math.floor(Math.random() * sources.length)]
      const severity = severities[Math.floor(Math.random() * severities.length)]

      return {
        source,
        severity,
        status: 'pending',
        alertTime: new Date().toLocaleString(),
        description: `测试告警 - ${source} - ${severity}`,
        details: `这是一个测试告警，用于验证告警中心的功能。\n来源：${source}\n级别：${severity}\n时间：${new Date().toLocaleString()}`,
        resolutionNote: ''
      }
    },

    // 处理测试告警
    async handleTest() {
      try {
        const testAlert = this.generateTestAlert()
        const response = await request({
          url: '/system/alert/test',
          method: 'post',
          data: testAlert
        })
        if (response.code === 200) {
          this.showMessage('success', '测试告警发送成功')
          this.getList()
        } else {
          this.showMessage('error', response.msg || '测试告警发送失败')
        }
      } catch (error) {
        console.error('发送测试告警失败:', error)
        this.showMessage('error', '发送测试告警失败')
      }
    },

    // 获取告警级别样式
    getSeverityType(severity) {
      const map = {
        critical: 'danger',
        high: 'danger',
        medium: 'warning',
        low: 'info',
        info: 'info'
      }
      return map[severity] || 'info'
    },

    // 获取严重程度文本
    getSeverityText(severity) {
      const map = {
        critical: '严重',
        high: '高危',
        medium: '中危',
        low: '低危',
        info: '信息'
      }
      return map[severity] || severity
    },

    // 获取告警状态样式
    getStatusType(status) {
      const map = {
        triggered: 'warning',  // 使用橙色，更醒目
        acknowledged: 'primary',  // 使用蓝色
        resolved: 'success',
        suppressed: 'info'
      }
      return map[status] || 'info'
    },

    // 消息提示
    showMessage(type, message) {
      Message({
        type,
        message
      })
    },

    // 确认框
    showConfirm(message, title = '提示') {
      return MessageBox.confirm(message, title, {
        type: 'warning'
      })
    },

    // 获取告警状态文本
    getStatusText(status) {
      const map = {
        triggered: '触发',
        acknowledged: '确认',
        resolved: '已解决',
        suppressed: '沉默'
      }
      return map[status] || status
    },

    // 获取告警源类型名称
    getSourceTypeName(sourceType) {
      const map = {
        muyun: '牧云',
        wangsu: '网宿',
        n9e: '夜莺',
        grafana: 'Grafana',
        prometheus: 'Prometheus',
        zabbix: 'Zabbix'
      }
      return map[sourceType] || sourceType
    },

    // 获取告警源类型颜色
    getSourceTypeColor(sourceType) {
      const map = {
        muyun: 'primary',
        wangsu: 'success',
        n9e: 'warning',
        grafana: 'info',
        prometheus: 'danger',
        zabbix: 'primary'
      }
      return map[sourceType] || 'default'
    },

    // 从标签中获取环境
    getEnvironmentFromTags(tags) {
      try {
        if (!tags) return null
        const tagsObj = typeof tags === 'string' ? JSON.parse(tags) : tags
        return tagsObj.environment || null
      } catch (e) {
        return null
      }
    },

    // 从标签中获取服务名称
    getServiceFromTags(tags) {
      try {
        if (!tags) return null
        const tagsObj = typeof tags === 'string' ? JSON.parse(tags) : tags
        return tagsObj.service || null
      } catch (e) {
        return null
      }
    },

    // 获取环境颜色
    getEnvironmentColor(environment) {
      const map = {
        production: 'danger',
        prod: 'danger',
        staging: 'warning',
        stage: 'warning',
        test: 'primary',  // 使用蓝色，更明显
        testing: 'primary',
        development: 'success',
        dev: 'success'
      }
      return map[environment?.toLowerCase()] || 'primary'
    },

    // 计算持续时间
    calculateDuration(row) {
      if (!row.occurredAt) return 0
      const endTime = row.resolvedAt || Date.now()
      return endTime - row.occurredAt
    },

    // 格式化持续时间
    formatDuration(durationMs) {
      if (!durationMs || durationMs <= 0) return '-'

      const seconds = Math.floor(durationMs / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)

      if (days > 0) {
        return `${days}天${hours % 24}小时`
      } else if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟`
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`
      } else {
        return `${seconds}秒`
      }
    },

    // 获取持续时间样式
    getDurationClass(durationMs) {
      if (!durationMs) return 'duration-normal'

      const hours = durationMs / (1000 * 60 * 60)
      if (hours > 24) return 'duration-critical'
      if (hours > 4) return 'duration-warning'
      return 'duration-normal'
    },

    // 获取时间前显示
    getTimeAgo(timestamp) {
      if (!timestamp) return ''

      const now = Date.now()
      const diff = now - timestamp
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)

      if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else if (minutes > 0) {
        return `${minutes}分钟前`
      } else {
        return '刚刚'
      }
    },

    // 检查是否已解决
    isResolved(status) {
      return ['resolved', 'suppressed'].includes(status)
    },

    // 获取用户头像
    getUserAvatar(userId) {
      // TODO: 实现用户头像获取逻辑
      return null
    },

    // 列表设置
    handleColumnSetting() {
      this.$message.info('列表设置功能待实现')
    },

    // 导出数据
    handleExport() {
      this.$message.info('导出功能待实现')
    },

    // 选择标签示例
    selectTagExample(value) {
      if (this.tagSearchMode === 'simple') {
        // 简单模式：直接设置或追加
        if (this.queryParams.tagSearch) {
          this.queryParams.tagSearch += ' AND ' + value
        } else {
          this.queryParams.tagSearch = value
        }
      } else {
        // 高级模式：添加到选中列表
        if (!this.selectedAdvancedTags.includes(value)) {
          this.selectedAdvancedTags.push(value)
        }
      }
      this.$message.success(`已选择标签: ${value}`)
    },

    // 高级模式方法
    toggleAdvancedTag(value) {
      const index = this.selectedAdvancedTags.indexOf(value)
      if (index > -1) {
        this.selectedAdvancedTags.splice(index, 1)
      } else {
        this.selectedAdvancedTags.push(value)
      }
    },

    removeAdvancedTag(value) {
      const index = this.selectedAdvancedTags.indexOf(value)
      if (index > -1) {
        this.selectedAdvancedTags.splice(index, 1)
      }
    },

    clearAllTags() {
      this.selectedAdvancedTags = []
      this.queryParams.tagSearch = ''
    },

    confirmAdvancedSelection() {
      const value = this.selectedAdvancedTags.join(' AND ')
      this.queryParams.tagSearch = value
      this.showAdvancedSelector = false
      if (this.selectedAdvancedTags.length > 0) {
        this.$message.success(`已选择 ${this.selectedAdvancedTags.length} 个标签`)
      }
    },

    // 标签搜索变化处理
    handleTagChange(value, searchType) {
      this.queryParams.tagSearch = value
      this.tagSearchType = searchType
    },

    // 获取严重程度类型
    getSeverityType(severity) {
      const map = {
        critical: 'danger',
        high: 'warning',
        medium: '',
        low: 'success',
        info: 'info'
      }
      return map[severity] || 'info'
    },

    // 获取严重程度文本
    getSeverityText(severity) {
      const map = {
        critical: '严重',
        high: '高',
        medium: '中',
        low: '低',
        info: '信息'
      }
      return map[severity] || severity
    },

    // 获取标签对象
    getTagsObject(tags) {
      try {
        return typeof tags === 'string' ? JSON.parse(tags) : tags
      } catch (e) {
        return null
      }
    },

    // 获取属性对象
    getAttributesObject(attributes) {
      try {
        return typeof attributes === 'string' ? JSON.parse(attributes) : attributes
      } catch (e) {
        return null
      }
    },

    // 根据标签键获取类型
    getTagTypeByKey(key) {
      const typeMap = {
        environment: 'danger',
        service: 'primary',
        team: 'success',
        monitor_system: 'info',
        datacenter: 'warning',
        cluster: 'info',
        severity: 'danger'
      }
      return typeMap[key] || ''
    },

    // 获取用于显示的标签数组
    getTagsForDisplay(tags) {
      try {
        if (!tags) return []
        const tagsObj = typeof tags === 'string' ? JSON.parse(tags) : tags

        // 转换为 key-value 数组，排除一些不需要显示的系统字段
        const excludeKeys = ['raw_data', 'original_data', 'internal_id']

        return Object.entries(tagsObj)
          .filter(([key, value]) => {
            return value !== null &&
                   value !== undefined &&
                   value !== '' &&
                   !excludeKeys.includes(key)
          })
          .map(([key, value]) => ({
            key: key,
            value: String(value).length > 15 ? String(value).substring(0, 15) + '...' : String(value)
          }))
          .sort((a, b) => {
            // 优先显示重要的标签
            const priority = ['environment', 'service', 'team', 'severity']
            const aIndex = priority.indexOf(a.key)
            const bIndex = priority.indexOf(b.key)

            if (aIndex !== -1 && bIndex !== -1) {
              return aIndex - bIndex
            } else if (aIndex !== -1) {
              return -1
            } else if (bIndex !== -1) {
              return 1
            } else {
              return a.key.localeCompare(b.key)
            }
          })
      } catch (e) {
        return []
      }
    },

    // 格式化原始数据
    formatRawData(rawData) {
      try {
        const data = typeof rawData === 'string' ? JSON.parse(rawData) : rawData
        return JSON.stringify(data, null, 2)
      } catch (e) {
        return rawData
      }
    },

    // 复制原始数据
    copyRawData() {
      const rawData = this.formatRawData(this.currentAlert.rawData)
      navigator.clipboard.writeText(rawData).then(() => {
        this.$message.success('原始数据已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 动态加载可用标签
    async loadAvailableTags() {
      this.tagsLoading = true
      try {
        const response = await request({
          url: '/alert/available-tags',
          method: 'get'
        })

        if (response.code === 200) {
          this.processAvailableTags(response.data)
        }
      } catch (error) {
        console.error('加载可用标签失败:', error)
        // 如果加载失败，使用默认标签
        this.setDefaultTags()
      }
      this.tagsLoading = false
    },

    // 处理可用标签数据
    processAvailableTags(tagsData) {
      // 处理环境标签
      this.environmentTags = (tagsData.environment || []).map(env => ({
        label: env,
        value: `environment:${env}`,
        type: this.getEnvironmentTagType(env),
        description: this.getEnvironmentDescription(env)
      }))

      // 处理服务标签
      this.serviceTags = (tagsData.service || []).map(service => ({
        label: service,
        value: `service:${service}`,
        type: 'primary',
        description: `${service}服务`
      }))

      // 处理团队标签
      this.teamTags = (tagsData.team || []).map(team => ({
        label: team,
        value: `team:${team}`,
        type: 'success',
        description: `${team}团队`
      }))

      // 处理监控系统标签
      this.monitorTags = (tagsData.monitor_system || []).map(monitor => ({
        label: monitor,
        value: `monitor_system:${monitor}`,
        type: 'info',
        description: `${monitor}监控系统`
      }))
    },

    // 获取环境标签类型
    getEnvironmentTagType(env) {
      const typeMap = {
        'production': 'danger',
        'prod': 'danger',
        'staging': 'warning',
        'stage': 'warning',
        'test': 'info',
        'testing': 'info',
        'development': 'success',
        'dev': 'success'
      }
      return typeMap[env.toLowerCase()] || 'default'
    },

    // 获取环境描述
    getEnvironmentDescription(env) {
      const descMap = {
        'production': '生产环境',
        'prod': '生产环境',
        'staging': '预发环境',
        'stage': '预发环境',
        'test': '测试环境',
        'testing': '测试环境',
        'development': '开发环境',
        'dev': '开发环境'
      }
      return descMap[env.toLowerCase()] || `${env}环境`
    },

    // 设置默认标签（备用方案）
    setDefaultTags() {
      this.environmentTags = [
        { label: 'production', value: 'environment:production', type: 'danger', description: '生产环境' },
        { label: 'staging', value: 'environment:staging', type: 'warning', description: '预发环境' },
        { label: 'test', value: 'environment:test', type: 'info', description: '测试环境' }
      ]

      this.serviceTags = [
        { label: 'api', value: 'service:api', type: 'primary', description: 'API服务' },
        { label: 'web', value: 'service:web', type: 'primary', description: 'Web服务' },
        { label: 'database', value: 'service:database', type: 'primary', description: '数据库服务' }
      ]

      this.teamTags = [
        { label: 'backend', value: 'team:backend', type: 'success', description: '后端团队' },
        { label: 'frontend', value: 'team:frontend', type: 'success', description: '前端团队' },
        { label: 'devops', value: 'team:devops', type: 'success', description: '运维团队' }
      ]
    },

    // 格式化JSON数据
    formatJson(jsonStr) {
      let parsed;
      try {
        // 如果已经是对象，直接格式化
        if (typeof jsonStr === 'object') {
          parsed = jsonStr;
        } else {
          // 尝试解析JSON字符串
          parsed = JSON.parse(jsonStr);
        }
        return JSON.stringify(parsed, null, 2);
      } catch (e) {
        console.error('JSON格式化失败:', e);
        return jsonStr; // 如果解析失败，返回原始字符串
      }
    },
    
    // 复制原始数据
    copyRawData() {
      if (!this.currentAlert.rawData) return;
      
      const textArea = document.createElement('textarea');
      textArea.value = typeof this.currentAlert.rawData === 'object' 
        ? JSON.stringify(this.currentAlert.rawData, null, 2) 
        : this.currentAlert.rawData;
        
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      this.$message.success('已复制到剪贴板');
    },
    
    // 切换原始数据展开状态
    toggleRawDataExpand() {
      this.rawDataExpanded = !this.rawDataExpanded;
    },
    handleJsonViewCommand(command) {
      switch (command) {
        case 'expand':
          this.rawDataExpanded = true;
          break;
        case 'format':
          this.formatJson(this.currentAlert.rawData);
          break;
        case 'tree':
          // Implement tree view functionality
          break;
        case 'plain':
          // Implement plain view functionality
          break;
        case 'copy':
          this.copyRawData();
          break;
        case 'download':
          this.downloadJson();
          break;
      }
    },
    downloadJson() {
      const blob = new Blob([JSON.stringify(this.currentAlert.rawData, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'alert_data.json';
      a.click();
      window.URL.revokeObjectURL(url);
    },
    updateJsonStyles() {
      this.highlightJsonSearch();
    },
    highlightJsonSearch() {
      if (!this.currentAlert.rawData) return;
      const searchText = this.jsonSearchQuery.trim().toLowerCase();
      
      // 如果搜索文本为空，重置为普通语法高亮
      if (!searchText) {
        this.jsonSearchResults = [];
        this.jsonSearchIndex = -1;
        this.updateJsonDisplay();
        return;
      }
      
      // 获取格式化的原始JSON文本（没有HTML标签）
      const formattedJson = typeof this.currentAlert.rawData === 'object' 
        ? JSON.stringify(this.currentAlert.rawData, null, 2)
        : String(this.currentAlert.rawData);
      
      // 查找所有匹配项
      const regex = new RegExp(this.escapeRegExp(searchText), 'gi');
      this.jsonSearchResults = [];
      let match;
      
      // 收集所有匹配项的位置
      while ((match = regex.exec(formattedJson)) !== null) {
        this.jsonSearchResults.push({
          index: match.index,
          length: match[0].length,
          text: match[0]
        });
      }
      
      // 如果没有找到匹配项，恢复普通显示
      if (this.jsonSearchResults.length === 0) {
        this.updateJsonDisplay();
        return;
      }
      
      // 设置当前匹配索引
      this.jsonSearchIndex = 0;
      
      // 全新的高亮实现方式
      // 将匹配项位置按照从后往前排序，这样替换时不会影响后续位置
      this.jsonSearchResults.sort((a, b) => b.index - a.index);
      
      // 对原始文本进行语法高亮（不包含搜索高亮）
      let syntaxHighlightedText = this.getSyntaxHighlightedJson(this.currentAlert.rawData);
      
      // 将HTML编码转换为安全的替代符号，以防替换时破坏HTML结构
      let workingText = syntaxHighlightedText;
      
      // 直接替换HTML字符串中的纯文本部分
      // 为确保安全，我们需要按字符位置操作并检查是否在HTML标签内
      
      // 简化方法：重新生成HTML，直接把需要高亮的文本包在高亮标签里
      let htmlParts = [];
      let lastIndex = 0;
      const lines = formattedJson.split('\n');
      let lineOffsets = [0];
      
      // 计算每行的起始位置
      for (let i = 0; i < lines.length - 1; i++) {
        lineOffsets.push(lineOffsets[i] + lines[i].length + 1); // +1 for newline
      }
      
      // 为每个匹配添加高亮标记
      for (let i = 0; i < formattedJson.length; i++) {
        // 检查当前位置是否是一个匹配的开始
        const matchAtCurrentPos = this.jsonSearchResults.find(
          match => match.index === i
        );
        
        if (matchAtCurrentPos) {
          // 在开始处添加高亮标签
          htmlParts.push('<span class="highlight">');
          
          // 添加匹配的文本
          htmlParts.push(this.escapeHtml(formattedJson.substr(i, matchAtCurrentPos.length)));
          
          // 在结束处添加结束标签
          htmlParts.push('</span>');
          
          // 跳过已处理的匹配文本
          i += matchAtCurrentPos.length - 1;
        } else {
          // 添加普通字符
          htmlParts.push(this.escapeHtml(formattedJson[i]));
        }
      }
      
      // 将生成的HTML转换为带有语法高亮的HTML
      let highlightedText = htmlParts.join('');
      
      // 应用语法高亮
      if (this.enableSyntaxHighlight) {
        // 将普通的转义字符替换为带有语法高亮的HTML
        highlightedText = highlightedText
          .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, (match) => {
            let cls = 'json-number';
            if (/^"/.test(match)) {
              if (/:$/.test(match)) {
                cls = 'json-key';
              } else {
                cls = 'json-string';
              }
            } else if (/true|false/.test(match)) {
              cls = 'json-boolean';
            } else if (/null/.test(match)) {
              cls = 'json-null';
            }
            // 保留已有的highlight标签
            return match.replace(/([^<]*)(<span class="highlight">)(.*?)(<\/span>)([^>]*)/g, 
              (all, before, startTag, content, endTag, after) => {
                if (before) before = `<span class="${cls}">${before}</span>`;
                if (after) after = `<span class="${cls}">${after}</span>`;
                return `${before || ''}${startTag}${content}${endTag}${after || ''}`;
              });
          });
      }
      
      // 设置最终的高亮HTML
      this.highlightedJson = highlightedText.replace(/\n/g, '<br>');
      
      // 滚动到第一个匹配项
      this.$nextTick(() => {
        this.scrollToHighlight();
      });
    },
    
    // 在文本节点中应用高亮
    applyHighlightsToTextNodes(element, searchResults, searchText) {
      // 此方法已被新方法替代
    },
    
    // 转义正则表达式特殊字符
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    },
    openJsonInNewWindow() {
      const win = window.open('', '_blank');
      win.document.write(`<pre>${this.formatJson(this.currentAlert.rawData)}</pre>`);
    },
    // 获取语法高亮的JSON
    getSyntaxHighlightedJson(json) {
      if (!json) return '';
      
      if (typeof json === 'string') {
        try {
          json = JSON.parse(json);
        } catch (e) {
          return this.escapeHtml(json);
        }
      }
      
      const formattedJson = JSON.stringify(json, null, 2);
      
      if (!this.enableSyntaxHighlight) {
        return this.escapeHtml(formattedJson);
      }
      
      // 基本语法高亮
      return formattedJson
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, (match) => {
          let cls = 'json-number';
          if (/^"/.test(match)) {
            if (/:$/.test(match)) {
              cls = 'json-key';
            } else {
              cls = 'json-string';
            }
          } else if (/true|false/.test(match)) {
            cls = 'json-boolean';
          } else if (/null/.test(match)) {
            cls = 'json-null';
          }
          return `<span class="${cls}">${match}</span>`;
        });
    },
    
    // 转义HTML特殊字符
    escapeHtml(unsafe) {
      return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
    },
    
    // 更新JSON显示
    updateJsonDisplay() {
      if (!this.currentAlert.rawData) {
        this.highlightedJson = '';
        return;
      }
      
      this.highlightedJson = this.getSyntaxHighlightedJson(this.currentAlert.rawData);
      
      // 如果有搜索关键字，应用搜索高亮
      if (this.jsonSearchQuery.trim()) {
        this.highlightJsonSearch();
      }
    },
    
    // 导航JSON搜索结果
    navigateJsonSearch(direction) {
      if (this.jsonSearchResults.length === 0) return;
      
      if (direction === 'next') {
        this.jsonSearchIndex = (this.jsonSearchIndex + 1) % this.jsonSearchResults.length;
      } else {
        this.jsonSearchIndex = this.jsonSearchIndex <= 0 ? 
          this.jsonSearchResults.length - 1 : this.jsonSearchIndex - 1;
      }
      
      this.scrollToHighlight();
    },
    
    // 滚动到当前高亮位置
    scrollToHighlight() {
      this.$nextTick(() => {
        const highlights = this.$refs.jsonViewer?.querySelectorAll('.highlight');
        if (highlights && highlights.length > this.jsonSearchIndex) {
          highlights[this.jsonSearchIndex].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      });
    },
    // 应用处理模板
    applyTemplate(templateId) {
      const templates = {
        fixed_service: '已对相关服务进行修复，问题已解决。服务恢复正常运行。',
        restart_service: '通过重启服务解决问题，服务已恢复正常。',
        config_change: '通过调整配置参数解决问题，已验证服务正常运行。',
        network_fixed: '网络连接问题已修复，连接恢复正常。',
        resource_issue: '临时资源不足导致，已增加资源配额，问题已解决。',
        custom: ''
      };
      
      if (templateId !== 'custom') {
        this.resolveForm.note = templates[templateId] || '';
      }
    },
    
    // 获取处理动作文本
    getResolveActionText() {
      const actionMap = {
        'resolve': '确认解决',
        'ignore': '确认忽略',
        'postpone': '确认延后'
      };
      return actionMap[this.resolveForm.action] || '确认';
    },
    // 获取解决类型样式
    getResolutionTypeColor(type) {
      const map = {
        auto_resolved: 'success',
        manual_resolved: 'primary',
        service_restart: 'warning',
        config_change: 'info',
        network_fixed: 'warning',
        resource_adjusted: 'info',
        false_positive: 'danger'
      }
      return map[type] || ''
    },

    // 获取解决类型文本
    getResolutionTypeText(type) {
      const map = {
        auto_resolved: '系统自动恢复',
        manual_resolved: '人工解决',
        service_restart: '服务重启',
        config_change: '配置调整',
        network_fixed: '网络修复',
        resource_adjusted: '资源调整',
        false_positive: '误报'
      }
      return map[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

// 统计卡片样式
.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .stat-content {
      display: flex;
      align-items: center;
      padding: 20px;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .stat-info {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: bold;
          line-height: 1;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }

    &.stat-card-total {
      .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      .stat-number {
        color: #667eea;
      }
    }

    &.stat-card-critical {
      .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      .stat-number {
        color: #f5576c;
      }
    }

    &.stat-card-pending {
      .stat-icon {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }
      .stat-number {
        color: #fcb69f;
      }
    }

    &.stat-card-resolved {
      .stat-icon {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      }
      .stat-number {
        color: #52c41a;
      }
    }
  }
}

// 搜索卡片样式
.search-card {
  margin-bottom: 20px;
  border-radius: 8px;

  .search-form {
    .el-form-item {
      margin-bottom: 15px;
    }
  }
}

// 工具栏样式
.toolbar-card {
  margin-bottom: 20px;
  border-radius: 8px;

  .toolbar-row {
    display: flex;
    align-items: center;

    .toolbar-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .el-switch {
        margin-right: 15px;
      }
    }
  }
}

// 表格卡片样式
.table-card {
  border-radius: 8px;

  .alert-table {
    .el-table__header {
      background-color: #fafafa;

      th {
        background-color: #fafafa !important;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }

    // 标签显示样式
    .tags-display {
      .tag-item {
        margin: 2px 3px 2px 0;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }

      .more-tags {
        margin: 2px 0;
        cursor: pointer;

        &:hover {
          transform: scale(1.05);
        }
      }

      .no-tags {
        color: #c0c4cc;
        font-size: 12px;
        font-style: italic;
      }
    }

    // 告警标题样式
    .alert-title {
      .title-link {
        font-weight: 500;
        font-size: 14px;

        &:hover {
          color: #409eff;
        }
      }

      .alert-meta {
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 8px;

        .event-id {
          font-size: 12px;
          color: #909399;
          font-family: 'Courier New', monospace;
        }
      }
    }

    // 告警源信息样式
    .source-info {
      .source-detail {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    // 服务信息样式
    .service-info {
      .service-name {
        margin-bottom: 3px;
      }

      .rule-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
        font-size: 12px;
      }

      .group-name {
        font-size: 11px;
        color: #909399;
      }

      .no-service {
        color: #c0c4cc;
      }
    }

    // 位置信息样式
    .location-info {
      .source-ident {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
        font-family: 'Courier New', monospace;
      }
    }

    // 时间信息样式
    .time-info {
      .occurred-time {
        font-size: 13px;
        color: #303133;
      }

      .time-ago {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    // 持续时间样式
    .duration-info {
      .duration-normal {
        color: #67c23a;
      }

      .duration-warning {
        color: #e6a23c;
      }

      .duration-critical {
        color: #f56c6c;
        font-weight: bold;
      }
    }

    // 处理人信息样式
    .resolver-info {
      display: flex;
      align-items: center;

      .resolver-name {
        margin-left: 8px;
        font-size: 12px;
        color: #606266;
      }
    }

    .no-resolver {
      color: #c0c4cc;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-cards {
    .el-col {
      margin-bottom: 15px;
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .search-form {
    .el-form-item {
      display: block;
      margin-right: 0;
    }
  }

  .toolbar-row {
    flex-direction: column;
    gap: 15px;

    .el-col {
      width: 100%;
    }
  }

  .alert-table {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

// 标签搜索样式
.tag-search-header {
  margin-bottom: 10px;

  .search-mode-toggle {
    .el-button {
      padding: 5px 15px;
      font-size: 12px;
    }
  }
}

.simple-search {
  margin-top: 8px;
}

.advanced-search {
  margin-top: 8px;
}

// 标签帮助样式
.tag-help {
  h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 14px;
  }

  h5 {
    margin: 10px 0 5px 0;
    color: #606266;
    font-size: 12px;
  }

  p {
    margin: 5px 0;
    color: #909399;
    font-size: 12px;
    line-height: 1.4;
  }

  code {
    background: #f5f7fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    color: #e6a23c;
  }

  .multi-tag-info {
    margin: 15px 0;
    padding: 10px;
    background: #f0f9ff;
    border-radius: 4px;
    border-left: 3px solid #409eff;
  }

  .tag-examples {
    margin: 10px 0;

    .tag-categories {
      .tag-category {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .category-name {
          font-size: 12px;
          color: #606266;
          margin-right: 8px;
          min-width: 50px;
        }

        .example-tag {
          margin: 2px 4px 2px 0;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .search-modes {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #ebeef5;
  }
}

// 高级选择器样式
.advanced-selector {
  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 14px;
    }
  }

  .selector-body {
    .tag-options {
      max-height: 200px;
      overflow-y: auto;

      .tag-option {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        margin-bottom: 5px;

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          background-color: #ecf5ff;
          border: 1px solid #b3d8ff;
        }

        .tag-description {
          margin-left: 10px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .selector-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;

    .selected-tags {
      margin-bottom: 10px;
      min-height: 24px;

      .label {
        font-size: 12px;
        color: #606266;
        margin-right: 8px;
      }

      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }

    .actions {
      text-align: right;

      .el-button {
        margin-left: 8px;
      }
    }
  }
}

// 抽屉样式
.alert-detail-drawer {
  padding: 0 20px 80px 20px;

  .detail-card {
    margin-bottom: 20px;
    border-radius: 8px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .alert-title-text {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .alert-description {
      line-height: 1.6;
      color: #606266;
    }

    .resolver-info {
      display: flex;
      align-items: center;

      .resolver-name {
        margin-left: 8px;
        color: #606266;
      }
    }

    .resolution-note {
      line-height: 1.6;
      color: #606266;
      background: #f5f7fa;
      padding: 10px;
      border-radius: 4px;
    }

    .tags-content {
      .tag-item {
        display: inline-block;
        margin: 5px 10px 5px 0;

        .tag-key {
          font-weight: 500;
          color: #606266;
          margin-right: 5px;
        }
      }

      .no-tags {
        color: #c0c4cc;
        font-style: italic;
      }
    }

    .attributes-content {
      .attribute-value {
        font-family: 'Courier New', monospace;
        background: #f5f7fa;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
      }

      .no-attributes {
        color: #c0c4cc;
        font-style: italic;
      }
    }

    .raw-data-content {
      .raw-data-pre {
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #606266;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 标签弹窗样式
.all-tags {
  .tag-item {
    margin: 3px 5px 3px 0;
    display: inline-block;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

.raw-data-content {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  font-family: 'SFMono-Regular', Monaco, Menlo, Consolas, 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  
  &.expanded {
    max-height: none;
  }
  
  &.syntax-highlight {
    background-color: #f8f8f8;
    
    .json-key {
      color: #881391;
    }
    
    .json-string {
      color: #1a1aa6;
    }
    
    .json-number {
      color: #1c00cf;
    }
    
    .json-boolean {
      color: #0d904f;
    }
    
    .json-null {
      color: #808080;
    }
    
    &.dark-theme {
      background-color: #1e1e1e;
      color: #d4d4d4;
      
      .json-key {
        color: #9cdcfe;
      }
      
      .json-string {
        color: #ce9178;
      }
      
      .json-number {
        color: #b5cea8;
      }
      
      .json-boolean {
        color: #569cd6;
      }
      
      .json-null {
        color: #569cd6;
      }
    }
  }
}

// 增强高亮样式
:deep(.highlight) {
  background-color: #ffff00 !important;
  color: #000000 !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
  margin: 0 -2px !important;
  border: 1px solid #ffa600 !important;
  font-weight: bold !important;
  display: inline-block !important;
  box-shadow: 0 0 2px rgba(255, 166, 0, 0.5) !important;
}

.dark-theme :deep(.highlight) {
  background-color: #ff9800 !important;
  color: #000000 !important;
  border: 1px solid #ffd740 !important;
}

.empty-data {
  color: #909399;
  text-align: center;
  padding: 40px 20px;
  font-size: 14px;
  background-color: #f8f8f8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  i {
    font-size: 32px;
    margin-bottom: 10px;
    color: #c0c4cc;
  }
  
  p {
    margin: 0;
  }
}

.json-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-controls {
      display: flex;
      align-items: center;
    }
  }

  .json-container {
    .json-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 8px;
      background-color: #f2f6fc;
      border-radius: 4px;

      .el-slider {
        width: 150px;
      }

      .search-results {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 12px;
        color: #606266;
        
        .search-navigation {
          margin: 0 5px;
        }
        
        .search-index {
          background-color: #ecf5ff;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 12px;
          color: #409eff;
        }
      }
    }

    .json-wrapper {
      overflow-y: auto;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      transition: all 0.3s;
      
      &:hover {
        border-color: #c6e2ff;
        box-shadow: 0 0 10px rgba(0, 149, 255, 0.1);
      }
    }

    .json-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;
    }
  }
}

.highlight {
  background-color: #ffd70080;
  border-radius: 2px;
  padding: 0 2px;
  margin: 0 -2px;
  display: inline-block;
  box-shadow: 0 0 0 1px #ffc10780;
}

// 添加告警处理对话框样式
.resolve-alert-container {
  .alert-summary {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    
    .alert-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .label {
        font-weight: bold;
        margin-right: 10px;
        color: #606266;
      }
      
      .value {
        font-size: 16px;
        font-weight: 500;
        margin-right: 10px;
        flex: 1;
      }
    }
    
    .alert-meta {
      display: flex;
      flex-wrap: wrap;
      
      .meta-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 5px;
        
        .label {
          color: #909399;
          margin-right: 5px;
        }
        
        .value {
          color: #606266;
        }
      }
    }
  }
  
  .resolve-options {
    display: flex;
    width: 100%;
    
    .el-radio-button {
      flex: 1;
      
      .el-radio-button__inner {
        width: 100%;
        text-align: center;
      }
    }
  }
}
</style>

<!-- 添加全局样式，确保高亮样式可以作用于动态生成的内容 -->
<style>
.highlight {
  background-color: #ffff00 !important;
  color: #000000 !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
  margin: 0 -2px !important;
  border: 1px solid #ffa600 !important;
  font-weight: bold !important;
  box-shadow: 0 0 2px rgba(255, 166, 0, 0.5) !important;
}

.dark-theme .highlight {
  background-color: #ff9800 !important;
  color: #000000 !important;
  border: 1px solid #ffd740 !important;
}
</style>