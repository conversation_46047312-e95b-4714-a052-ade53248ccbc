package com.wiwj.securio.intelmgr.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wiwj.securio.intelmgr.mapper.PatchReferenceMapper;
import com.wiwj.securio.intelmgr.domain.PatchReference;
import com.wiwj.securio.intelmgr.service.IPatchReferenceService;

/**
 * 补丁参考链接Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class PatchReferenceServiceImpl implements IPatchReferenceService {
    @Autowired
    private PatchReferenceMapper patchReferenceMapper;

    /**
     * 查询补丁参考链接
     *
     * @param id 补丁参考链接主键
     * @return 补丁参考链接
     */
    @Override
    public PatchReference selectPatchReferenceById(Integer id) {
        return patchReferenceMapper.selectPatchReferenceById(id);
    }

    /**
     * 查询补丁参考链接列表
     *
     * @param patchReference 补丁参考链接
     * @return 补丁参考链接
     */
    @Override
    public List<PatchReference> selectPatchReferenceList(PatchReference patchReference) {
        return patchReferenceMapper.selectPatchReferenceList(patchReference);
    }

    /**
     * 新增补丁参考链接
     *
     * @param patchReference 补丁参考链接
     * @return 结果
     */
    @Override
    public int insertPatchReference(PatchReference patchReference) {
        return patchReferenceMapper.insertPatchReference(patchReference);
    }

    /**
     * 修改补丁参考链接
     *
     * @param patchReference 补丁参考链接
     * @return 结果
     */
    @Override
    public int updatePatchReference(PatchReference patchReference) {
        return patchReferenceMapper.updatePatchReference(patchReference);
    }

    /**
     * 批量删除补丁参考链接
     *
     * @param ids 需要删除的补丁参考链接主键
     * @return 结果
     */
    @Override
    public int deletePatchReferenceByIds(Integer[] ids) {
        return patchReferenceMapper.deletePatchReferenceByIds(ids);
    }

    /**
     * 删除补丁参考链接信息
     *
     * @param id 补丁参考链接主键
     * @return 结果
     */
    @Override
    public int deletePatchReferenceById(Integer id) {
        return patchReferenceMapper.deletePatchReferenceById(id);
    }
}
