package com.wiwj.securio.agent.mapper;

import java.util.List;
import com.wiwj.securio.agent.domain.InventoryUserInfo;

/**
 * 存储用户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
public interface InventoryUserInfoMapper {
    /**
     * 查询存储用户信息
     *
     * @param id 存储用户信息主键
     * @return 存储用户信息
     */
    public InventoryUserInfo selectInventoryUserInfoById(Long id);

    /**
     * 查询存储用户信息列表
     *
     * @param inventoryUserInfo 存储用户信息
     * @return 存储用户信息集合
     */
    public List<InventoryUserInfo> selectInventoryUserInfoList(InventoryUserInfo inventoryUserInfo);

    /**
     * 新增存储用户信息
     *
     * @param inventoryUserInfo 存储用户信息
     * @return 结果
     */
    public int insertInventoryUserInfo(InventoryUserInfo inventoryUserInfo);

    /**
     * 修改存储用户信息
     *
     * @param inventoryUserInfo 存储用户信息
     * @return 结果
     */
    public int updateInventoryUserInfo(InventoryUserInfo inventoryUserInfo);

    /**
     * 删除存储用户信息
     *
     * @param id 存储用户信息主键
     * @return 结果
     */
    public int deleteInventoryUserInfoById(Long id);

    /**
     * 批量删除存储用户信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryUserInfoByIds(Long[] ids);

    /**
     * 批量新增存储用户信息
     *
     * @param inventoryUserInfoList 存储用户信息列表
     * @return 结果
     */
    public int batchInsertInventoryUserInfo(List<InventoryUserInfo> inventoryUserInfoList);

    /**
     * 根据Agent ID删除存储用户信息
     *
     * @param agentId Agent ID
     * @return 结果
     */
    public int deleteInventoryUserInfoByAgentId(String agentId);
}
