package com.wiwj.system.domain;

import com.wiwj.common.annotation.Excel;
import com.wiwj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 索引条目信息对象 index_category_item
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public class IndexCategoryItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** url，text */
    @Excel(name = "url，text")
    private String type;

    /** 栏目ID */
    @Excel(name = "栏目ID")
    private Long categoryId;

    /** 图标 */
    @Excel(name = "图标")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    private Long sortNum;

    /** 背景样式 */
    @Excel(name = "背景样式")
    private String bgStyle;

    /** 对应字典项 */
    @Excel(name = "对应字典项")
    private String groupName;

    /** 状态（0正常，1禁用） */
    @Excel(name = "状态", readConverterExp = "0=正常，1禁用")
    private Long status;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Long createAt;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private Long updateAt;

    /** 逻辑删除：（0[false]可用，[1]true删除） */
    @Excel(name = "逻辑删除：", readConverterExp = "0=[false]可用，[1]true删除")
    private Long isDel;

    /** 栏目名称 */
    @Excel(name = "栏目名称")
    private String categoryName;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }
    public void setContent(String content){
        this.content = content;
    }

    public String getContent(){
        return content;
    }
    public void setType(String type){
        this.type = type;
    }

    public String getType(){
        return type;
    }
    public void setCategoryId(Long categoryId){
        this.categoryId = categoryId;
    }

    public Long getCategoryId(){
        return categoryId;
    }
    public void setIcon(String icon){
        this.icon = icon;
    }

    public String getIcon(){
        return icon;
    }
    public void setSortNum(Long sortNum){
        this.sortNum = sortNum;
    }

    public Long getSortNum(){
        return sortNum;
    }
    public void setBgStyle(String bgStyle){
        this.bgStyle = bgStyle;
    }

    public String getBgStyle(){
        return bgStyle;
    }
    public void setGroupName(String groupName){
        this.groupName = groupName;
    }

    public String getGroupName(){
        return groupName;
    }
    public void setStatus(Long status){
        this.status = status;
    }

    public Long getStatus(){
        return status;
    }
    public void setCreateAt(Long createAt){
        this.createAt = createAt;
    }

    public Long getCreateAt(){
        return createAt;
    }
    public void setUpdateAt(Long updateAt){
        this.updateAt = updateAt;
    }

    public Long getUpdateAt(){
        return updateAt;
    }
    public void setIsDel(Long isDel){
        this.isDel = isDel;
    }

    public Long getIsDel(){
        return isDel;
    }
    public void setCategoryName(String categoryName){
        this.categoryName = categoryName;
    }

    public String getCategoryName(){
        return categoryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("content", getContent())
                .append("type", getType())
                .append("categoryId", getCategoryId())
                .append("icon", getIcon())
                .append("sortNum", getSortNum())
                .append("bgStyle", getBgStyle())
                .append("groupName", getGroupName())
                .append("status", getStatus())
                .append("createAt", getCreateAt())
                .append("createBy", getCreateBy())
                .append("updateAt", getUpdateAt())
                .append("updateBy", getUpdateBy())
                .append("isDel", getIsDel())
                .append("categoryName", getCategoryName())
                .toString();
    }
}
